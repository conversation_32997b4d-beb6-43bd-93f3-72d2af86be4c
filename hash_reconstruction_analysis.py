#!/usr/bin/env python3
"""
Hash Reconstruction Analysis
============================

Now that we know the Bellscoin hash contains an embedded timestamp at position 8,
let's analyze the other parts to see if they contain encoded information that
could lead us to the private key.

Hash: e5be24df57c43a82d15c2f06bda961296948f8f8eb48501bed1efb929afe0698
      ^^^^^^^^57c43a82^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
      Part1   Timestamp Part3                    Part4

Target Genesis Public Key: 040184710fa689ad5023690c80f3a49c8f13f8d45b8c857fbcbc8bc4a8e4d3eb4b10f4d4604fa08dce601aaf0f470216fe1b51850b4acf21b179c45070ac7b03a9
"""

import hashlib
import binascii
import struct
import datetime
from typing import Optional, List, Tuple
from ecdsa import SigningKey, SECP256k1

# Target genesis public key
TARGET_PUBKEY = "040184710fa689ad5023690c80f3a49c8f13f8d45b8c857fbcbc8bc4a8e4d3eb4b10f4d4604fa08dce601aaf0f470216fe1b51850b4acf21b179c45070ac7b03a9"

# The hash and its components
BELLSCOIN_HASH = "e5be24df57c43a82d15c2f06bda961296948f8f8eb48501bed1efb929afe0698"
TIMESTAMP_HEX = "57c43a82"
TIMESTAMP_VALUE = 1472477826

# Split the hash into parts
PART1 = BELLSCOIN_HASH[:8]      # e5be24df
PART2 = BELLSCOIN_HASH[8:16]    # 57c43a82 (timestamp)
PART3 = BELLSCOIN_HASH[16:32]   # d15c2f06bda96129
PART4 = BELLSCOIN_HASH[32:48]   # 6948f8f8eb48501b
PART5 = BELLSCOIN_HASH[48:]     # ed1efb929afe0698

# Other important values
GENESIS_TIMESTAMP_BELLS = 1383509530
GENESIS_TIMESTAMP_DOGE = 1386325540
GENESIS_NONCE_BELLS = 44481
GENESIS_NONCE_DOGE = 99943
SCRIPT_CONSTANT = 486604799

def test_private_key(private_key_hex: str) -> bool:
    """Test if a private key generates the target public key"""
    try:
        if len(private_key_hex) != 64:
            return False
            
        private_key_int = int(private_key_hex, 16)
        if private_key_int <= 0 or private_key_int >= 0xFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFEBAAEDCE6AF48A03BBFD25E8CD0364141:
            return False
            
        signing_key = SigningKey.from_string(binascii.unhexlify(private_key_hex), curve=SECP256k1)
        verifying_key = signing_key.get_verifying_key()
        public_key_bytes = verifying_key.to_string()
        public_key_hex = "04" + public_key_bytes.hex()
        
        return public_key_hex.lower() == TARGET_PUBKEY.lower()
        
    except Exception as e:
        return False

def test_candidate(name: str, private_key_hex: str) -> bool:
    """Test a candidate and print results"""
    print(f"\n🔍 Testing: {name}")
    print(f"   Private Key: {private_key_hex}")
    
    if test_private_key(private_key_hex):
        print(f"   🎯 TREASURE FOUND! {name}")
        return True
    else:
        print(f"   ❌ No match")
        return False

def analyze_hash_parts():
    """Analyze each part of the hash for encoded information"""
    print("🧩 HASH PARTS ANALYSIS")
    print("=" * 50)
    
    print(f"Complete Hash: {BELLSCOIN_HASH}")
    print(f"Part 1:        {PART1} (bytes 0-3)")
    print(f"Part 2:        {PART2} (bytes 4-7) = Timestamp {TIMESTAMP_VALUE}")
    print(f"Part 3:        {PART3} (bytes 8-15)")
    print(f"Part 4:        {PART4} (bytes 16-23)")
    print(f"Part 5:        {PART5} (bytes 24-31)")
    
    # Analyze each part as potential encoded data
    parts = [
        ("Part 1", PART1),
        ("Part 3", PART3), 
        ("Part 4", PART4),
        ("Part 5", PART5),
    ]
    
    for part_name, part_hex in parts:
        print(f"\n--- {part_name}: {part_hex} ---")
        
        # As integer
        part_int = int(part_hex, 16)
        print(f"  As integer: {part_int}")
        
        # Check if it could be a timestamp
        if 1000000000 <= part_int <= 2000000000:  # Reasonable timestamp range
            try:
                dt = datetime.datetime.fromtimestamp(part_int)
                print(f"  As timestamp: {dt}")
            except:
                pass
        
        # As 32-bit integers (if long enough)
        if len(part_hex) >= 8:
            try:
                # Big-endian
                int_be = struct.unpack('>I', bytes.fromhex(part_hex[:8]))[0]
                print(f"  First 4 bytes as uint32 (BE): {int_be}")
                
                # Little-endian
                int_le = struct.unpack('<I', bytes.fromhex(part_hex[:8]))[0]
                print(f"  First 4 bytes as uint32 (LE): {int_le}")
                
                # Check if these could be timestamps
                for val, endian in [(int_be, "BE"), (int_le, "LE")]:
                    if 1000000000 <= val <= 2000000000:
                        try:
                            dt = datetime.datetime.fromtimestamp(val)
                            print(f"    {endian} timestamp: {dt}")
                        except:
                            pass
            except:
                pass
        
        # Check for known constants
        known_constants = {
            GENESIS_TIMESTAMP_BELLS: "Bellscoin Genesis Timestamp",
            GENESIS_TIMESTAMP_DOGE: "Dogecoin Genesis Timestamp", 
            GENESIS_NONCE_BELLS: "Bellscoin Genesis Nonce",
            GENESIS_NONCE_DOGE: "Dogecoin Genesis Nonce",
            SCRIPT_CONSTANT: "Script Constant",
        }
        
        for const_val, const_name in known_constants.items():
            const_hex = hex(const_val)[2:]
            if const_hex in part_hex:
                print(f"  Contains {const_name}: {const_hex}")

def generate_reconstruction_candidates() -> List[Tuple[str, str]]:
    """Generate candidates by reconstructing the hash with different values"""
    candidates = []
    
    # Try replacing the timestamp with other known values
    replacement_values = {
        "Bells Genesis Timestamp": GENESIS_TIMESTAMP_BELLS,
        "Doge Genesis Timestamp": GENESIS_TIMESTAMP_DOGE,
        "Bells Nonce": GENESIS_NONCE_BELLS,
        "Doge Nonce": GENESIS_NONCE_DOGE,
        "Script Constant": SCRIPT_CONSTANT,
        "Zero": 0,
        "Max 32-bit": 0xFFFFFFFF,
    }
    
    for name, value in replacement_values.items():
        # Replace timestamp part with new value
        new_timestamp_hex = struct.pack('>I', value & 0xFFFFFFFF).hex()
        reconstructed = PART1 + new_timestamp_hex + PART3 + PART4 + PART5
        candidates.append((f"Replace timestamp with {name}", reconstructed))
    
    # Try using different parts as the private key
    candidates.extend([
        ("Part 1 + Part 3", PART1 + PART3),
        ("Part 3 + Part 4", PART3 + PART4),
        ("Part 4 + Part 5", PART4 + PART5),
        ("Part 1 + Part 5", PART1 + PART5),
        ("All parts except timestamp", PART1 + PART3 + PART4 + PART5),
    ])
    
    # Try XOR operations between parts
    try:
        part1_int = int(PART1, 16)
        part3_int = int(PART3, 16)
        part4_int = int(PART4, 16)
        part5_int = int(PART5, 16)
        
        candidates.extend([
            ("Part1 XOR Part3", hex(part1_int ^ part3_int)[2:].zfill(64)),
            ("Part3 XOR Part4", hex(part3_int ^ part4_int)[2:].zfill(64)),
            ("Part4 XOR Part5", hex(part4_int ^ part5_int)[2:].zfill(64)),
            ("All parts XOR", hex(part1_int ^ part3_int ^ part4_int ^ part5_int)[2:].zfill(64)),
        ])
    except:
        pass
    
    # Try hashing combinations of parts
    candidates.extend([
        ("SHA256(Part1 + Part3)", hashlib.sha256((PART1 + PART3).encode()).hexdigest()),
        ("SHA256(Part3 + Part4)", hashlib.sha256((PART3 + PART4).encode()).hexdigest()),
        ("SHA256(All parts)", hashlib.sha256((PART1 + PART3 + PART4 + PART5).encode()).hexdigest()),
        ("SHA256('Nintondo' + Part1)", hashlib.sha256(f"Nintondo{PART1}".encode()).hexdigest()),
        ("SHA256('Nintondo' + Part3)", hashlib.sha256(f"Nintondo{PART3}".encode()).hexdigest()),
    ])
    
    # Try interpreting parts as little-endian and reconstructing
    try:
        # Reverse byte order of each part
        part1_rev = ''.join([PART1[i:i+2] for i in range(0, len(PART1), 2)][::-1])
        part3_rev = ''.join([PART3[i:i+2] for i in range(0, len(PART3), 2)][::-1])
        part4_rev = ''.join([PART4[i:i+2] for i in range(0, len(PART4), 2)][::-1])
        part5_rev = ''.join([PART5[i:i+2] for i in range(0, len(PART5), 2)][::-1])
        
        candidates.extend([
            ("Byte-reversed parts", part1_rev + part3_rev + part4_rev + part5_rev),
            ("SHA256(Byte-reversed)", hashlib.sha256((part1_rev + part3_rev + part4_rev + part5_rev).encode()).hexdigest()),
        ])
    except:
        pass
    
    return candidates

def main():
    """Main execution function"""
    print("🔧 HASH RECONSTRUCTION ANALYSIS")
    print("=" * 70)
    print(f"Target Public Key: {TARGET_PUBKEY}")
    print(f"Bellscoin Hash: {BELLSCOIN_HASH}")
    print("=" * 70)
    
    # Analyze hash parts
    analyze_hash_parts()
    
    # Test reconstruction candidates
    print(f"\n🧪 TESTING RECONSTRUCTION CANDIDATES")
    print("=" * 50)
    
    candidates = generate_reconstruction_candidates()
    total_candidates = len(candidates)
    
    print(f"Testing {total_candidates} reconstruction candidates...")
    
    for i, (name, private_key_hex) in enumerate(candidates, 1):
        print(f"\n[{i}/{total_candidates}] ", end="")
        
        if test_candidate(name, private_key_hex):
            print(f"\n🎉 SUCCESS! The treasure has been found!")
            print(f"🎉 Method: {name}")
            print(f"🎉 Private key: {private_key_hex}")
            return private_key_hex
    
    print(f"\n❌ No matches found in {total_candidates} reconstruction candidates")
    print("💡 The hash structure suggests it's constructed, but the private key derivation is still hidden")
    return None

if __name__ == "__main__":
    result = main()
    if result:
        print(f"\n🏆 FINAL RESULT: {result}")
