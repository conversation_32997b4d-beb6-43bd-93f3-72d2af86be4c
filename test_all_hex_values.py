#!/usr/bin/env python3
"""
TEST ALL HEX VALUES FROM CODEBASE
=================================

Test every hex value we found in the codebase search as potential private keys.

Target Genesis Public Key: 040184710fa689ad5023690c80f3a49c8f13f8d45b8c857fbcbc8bc4a8e4d3eb4b10f4d4604fa08dce601aaf0f470216fe1b51850b4acf21b179c45070ac7b03a9
"""

import hashlib
from ecdsa import SigningKey, SECP256k1

# Target genesis public key
TARGET_PUBKEY = "040184710fa689ad5023690c80f3a49c8f13f8d45b8c857fbcbc8bc4a8e4d3eb4b10f4d4604fa08dce601aaf0f470216fe1b51850b4acf21b179c45070ac7b03a9"

def sha256_hash(text: str) -> str:
    """Generate SHA256 hash of text"""
    return hashlib.sha256(text.encode()).hexdigest()

def test_private_key(private_key_hex: str) -> bool:
    """Test if a hex string generates the target public key"""
    try:
        if private_key_hex.startswith('0x'):
            private_key_hex = private_key_hex[2:]
        
        if len(private_key_hex) != 64:
            # Pad shorter hex values
            if len(private_key_hex) < 64:
                private_key_hex = private_key_hex.zfill(64)
            else:
                return False
            
        private_key_int = int(private_key_hex, 16)
        
        if private_key_int <= 0 or private_key_int >= 0xFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFEBAAEDCE6AF48A03BBFD25E8CD0364141:
            return False
        
        sk = SigningKey.from_secret_exponent(private_key_int, curve=SECP256k1)
        vk = sk.get_verifying_key()
        
        public_key_bytes = b'\x04' + vk.to_string()
        public_key_hex = public_key_bytes.hex()
        
        return public_key_hex.lower() == TARGET_PUBKEY.lower()
        
    except Exception as e:
        return False

def test_all_discovered_hex_values():
    """Test all hex values discovered in the codebase"""
    print("🔍 TESTING ALL DISCOVERED HEX VALUES")
    print("=" * 60)
    
    # All hex values found in the codebase search
    hex_values = [
        # Main checkpoint and genesis hashes
        ("Bellscoin checkpoint", "e5be24df57c43a82d15c2f06bda961296948f8f8eb48501bed1efb929afe0698"),
        ("Dogecoin genesis hash", "1a91e3dace36e2be3bf030a65679fe821aa1d6ef92e7c9902eb318182c355691"),
        
        # From Dogecoin main.cpp comments
        ("Dogecoin hashGenesisBlock", "9b7bce58999062b63bfb18586813c42491fa32f4591d8d3043cb4fa9e551541b"),
        ("Dogecoin hashMerkleRoot", "6f80efd038566e1e3eab3e1d38131604d06481e77f2462235c6a9a94b1f8abf9"),
        ("Dogecoin PoW hash", "caeb449903dc4f0e0ee2"),
        ("Dogecoin transaction hash", "6f80efd038"),
        
        # From Bellscoin test checkpoints
        ("Bellscoin checkpoint p1500", "841a2965955dd288cfa707a755d05a54e45f8bd476835ec9af4402a2b59a2967"),
        ("Bellscoin checkpoint p120000", "bd9d26924f05f6daa7f0155f32828ec89e8e29cee9e7121b026a7a3552ac6131"),
        
        # From advanced_treasure_hunt.py
        ("Bitcoin genesis pubkey", "04678afdb0fe5548271967f1a67130b7105cd6a828e03909a67962e0ea1f61deb649f6bc3f4cef38c4f35504e51ec112de5c384df7ba0b8d578a4c702b6bf11d5f"),
        
        # From nintondo_deep_dive.py
        ("Nintondo merkle", "97ddfbbae6be97fd6cdf3e7ca13232a3afff2353e29badfab7f73011edd4ced9"),
        
        # Merkle root from comprehensive hunt
        ("Comprehensive merkle", "5b2a3f53f605d62c53e62932dac6925e3d74afa5a4b459745c36d42d0ed26a69"),
        
        # Numeric constants as hex
        ("Script constant", "486604799"),
        ("Bellscoin timestamp", "1383509530"),
        ("Dogecoin timestamp", "1386325540"),
        ("Bellscoin nonce", "44481"),
        ("Dogecoin nonce", "99943"),
        ("Comment nonce", "11288888"),
        ("Comment timestamp", "1369199888"),
        ("nBits value", "1e0ffff0"),
        ("nBits extended", "0x1e0ffff0"),
        
        # Nintondo in hex
        ("Nintondo hex", "4e696e746f6e646f"),
        ("Nintendo hex", "4e696e74656e646f"),
        
        # Other hex patterns found
        ("Short PoW", "caeb449903dc4f0e0ee2"),
        ("Merkle short", "6f80efd038"),
        
        # Version bytes and constants
        ("Bellscoin version", "19"),
        ("Dogecoin version", "1e"),
        ("Bitcoin version", "00"),
        ("Script version", "05"),
        
        # Our target public key parts
        ("Target pubkey X", "0184710fa689ad5023690c80f3a49c8f13f8d45b8c857fbcbc8bc4a8e4d3eb4b"),
        ("Target pubkey Y", "10f4d4604fa08dce601aaf0f470216fe1b51850b4acf21b179c45070ac7b03a9"),
    ]
    
    for name, hex_value in hex_values:
        print(f"\n🔍 Testing {name}: {hex_value}")
        
        # Test direct hex value
        if test_private_key(hex_value):
            print(f"🎯 TREASURE FOUND! Direct hex: {name} = {hex_value}")
            return hex_value
        
        # Test without 0x prefix if present
        clean_hex = hex_value.replace('0x', '')
        if clean_hex != hex_value and test_private_key(clean_hex):
            print(f"🎯 TREASURE FOUND! Clean hex: {name} = {clean_hex}")
            return clean_hex
        
        # Test SHA256 hash of the hex value
        hash_result = sha256_hash(hex_value)
        if test_private_key(hash_result):
            print(f"🎯 TREASURE FOUND! SHA256({name}): {hash_result}")
            return hash_result
        
        # Test with Nintondo prefix
        nintondo_combo = sha256_hash("Nintondo" + hex_value)
        if test_private_key(nintondo_combo):
            print(f"🎯 TREASURE FOUND! Nintondo+{name}: {nintondo_combo}")
            return nintondo_combo
        
        # Test with Nintondo suffix
        hex_nintondo = sha256_hash(hex_value + "Nintondo")
        if test_private_key(hex_nintondo):
            print(f"🎯 TREASURE FOUND! {name}+Nintondo: {hex_nintondo}")
            return hex_nintondo
    
    return None

def test_hex_combinations():
    """Test combinations of important hex values"""
    print("\n🔗 TESTING HEX COMBINATIONS")
    print("=" * 60)
    
    # Key hex values for combinations
    key_hex_values = [
        "e5be24df57c43a82d15c2f06bda961296948f8f8eb48501bed1efb929afe0698",  # Bellscoin checkpoint
        "4e696e746f6e646f",  # Nintondo
        "486604799",         # Script constant
        "44481",            # Bellscoin nonce
        "99943",            # Dogecoin nonce
        "1e0ffff0",         # nBits
    ]
    
    # Test pairs
    for i, hex1 in enumerate(key_hex_values):
        for j, hex2 in enumerate(key_hex_values):
            if i != j:  # Don't combine with itself
                combinations = [
                    hex1 + hex2,
                    hex2 + hex1,
                ]
                
                for combo in combinations:
                    print(f"Testing combination: {hex1[:16]}...+{hex2[:16]}...")
                    
                    # Test direct combination
                    if test_private_key(combo):
                        print(f"🎯 TREASURE FOUND! Combination: {combo}")
                        return combo
                    
                    # Test SHA256 of combination
                    combo_hash = sha256_hash(combo)
                    if test_private_key(combo_hash):
                        print(f"🎯 TREASURE FOUND! SHA256(combination): {combo_hash}")
                        return combo_hash
    
    return None

def test_hex_arithmetic():
    """Test arithmetic operations on hex values"""
    print("\n🧮 TESTING HEX ARITHMETIC")
    print("=" * 60)
    
    # Key numeric values
    key_numbers = [
        ("Bellscoin checkpoint", int("e5be24df57c43a82d15c2f06bda961296948f8f8eb48501bed1efb929afe0698", 16)),
        ("Script constant", 486604799),
        ("Bellscoin nonce", 44481),
        ("Dogecoin nonce", 99943),
        ("Bellscoin timestamp", 1383509530),
        ("Dogecoin timestamp", 1386325540),
    ]
    
    for name1, num1 in key_numbers:
        for name2, num2 in key_numbers:
            if name1 != name2:
                operations = [
                    (f"{name1} + {name2}", num1 + num2),
                    (f"{name1} - {name2}", abs(num1 - num2)),  # Use absolute value
                    (f"{name1} XOR {name2}", num1 ^ num2),
                ]
                
                for op_name, result in operations:
                    if result > 0:
                        result_hex = hex(result)[2:].zfill(64)
                        print(f"Testing {op_name}: {result_hex[:32]}...")
                        
                        if test_private_key(result_hex):
                            print(f"🎯 TREASURE FOUND! {op_name}: {result_hex}")
                            return result_hex
    
    return None

def test_reversed_hex():
    """Test reversed hex values"""
    print("\n🔄 TESTING REVERSED HEX VALUES")
    print("=" * 60)
    
    important_hex = [
        ("Bellscoin checkpoint", "e5be24df57c43a82d15c2f06bda961296948f8f8eb48501bed1efb929afe0698"),
        ("Target pubkey X", "0184710fa689ad5023690c80f3a49c8f13f8d45b8c857fbcbc8bc4a8e4d3eb4b"),
        ("Target pubkey Y", "10f4d4604fa08dce601aaf0f470216fe1b51850b4acf21b179c45070ac7b03a9"),
    ]
    
    for name, hex_value in important_hex:
        # Reverse the hex string
        reversed_hex = hex_value[::-1]
        print(f"Testing reversed {name}: {reversed_hex[:32]}...")
        
        if test_private_key(reversed_hex):
            print(f"🎯 TREASURE FOUND! Reversed {name}: {reversed_hex}")
            return reversed_hex
        
        # Test SHA256 of reversed
        reversed_hash = sha256_hash(reversed_hex)
        if test_private_key(reversed_hash):
            print(f"🎯 TREASURE FOUND! SHA256(reversed {name}): {reversed_hash}")
            return reversed_hash
    
    return None

def main():
    """Main execution function"""
    print("🏴‍☠️ COMPREHENSIVE HEX VALUE TESTING")
    print("=" * 60)
    print("Testing every hex value found in the codebase...")
    print(f"Target: {TARGET_PUBKEY[:32]}...")
    
    # Test different approaches
    approaches = [
        test_all_discovered_hex_values,
        test_hex_combinations,
        test_hex_arithmetic,
        test_reversed_hex,
    ]
    
    for approach_func in approaches:
        result = approach_func()
        if result:
            print(f"\n🏆 TREASURE DISCOVERED: {result}")
            return result
    
    print(f"\n💔 No treasure found in hex value testing")
    print("🤔 The private key might require an even more sophisticated approach...")

if __name__ == "__main__":
    main()
