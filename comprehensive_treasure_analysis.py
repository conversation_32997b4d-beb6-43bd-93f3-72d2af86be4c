#!/usr/bin/env python3
"""
Comprehensive Treasure Hunt Analysis
====================================

Using all the new information provided:
- Dog<PERSON>oin genesis address: DQmCZQo3thCvTxkyAhPHfY7DVLqFtJ2ji6
- Bellscoin scriptsig contains "Nintondo": 4e696e746f6e646f
- The 88 BEL are UNSPENT - meaning the private key has never been used!
- Our private key: e5be24df57c43a82d15c2f06bda961296948f8f8eb48501bed1efb929afe0698

Target Genesis Public Key: 040184710fa689ad5023690c80f3a49c8f13f8d45b8c857fbcbc8bc4a8e4d3eb4b10f4d4604fa08dce601aaf0f470216fe1b51850b4acf21b179c45070ac7b03a9
"""

import hashlib
import binascii
import base58
from typing import Optional, List, Tuple
from ecdsa import SigningKey, SECP256k1

# Key information
PRIVATE_KEY = "e5be24df57c43a82d15c2f06bda961296948f8f8eb48501bed1efb929afe0698"
TARGET_PUBKEY = "040184710fa689ad5023690c80f3a49c8f13f8d45b8c857fbcbc8bc4a8e4d3eb4b10f4d4604fa08dce601aaf0f470216fe1b51850b4acf21b179c45070ac7b03a9"
DOGECOIN_GENESIS_ADDRESS = "DQmCZQo3thCvTxkyAhPHfY7DVLqFtJ2ji6"
NINTONDO_HEX = "4e696e746f6e646f"  # "Nintondo"
BELLSCOIN_SCRIPTSIG = "04ffff001d0104084e696e746f6e646f"

def test_private_key(private_key_hex: str) -> bool:
    """Test if a private key generates the target public key"""
    try:
        if len(private_key_hex) != 64:
            return False
            
        private_key_int = int(private_key_hex, 16)
        if private_key_int <= 0 or private_key_int >= 0xFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFEBAAEDCE6AF48A03BBFD25E8CD0364141:
            return False
            
        signing_key = SigningKey.from_string(binascii.unhexlify(private_key_hex), curve=SECP256k1)
        verifying_key = signing_key.get_verifying_key()
        public_key_bytes = verifying_key.to_string()
        public_key_hex = "04" + public_key_bytes.hex()
        
        return public_key_hex.lower() == TARGET_PUBKEY.lower()
        
    except Exception as e:
        return False

def test_candidate(name: str, private_key_hex: str) -> bool:
    """Test a candidate and print results"""
    print(f"\n🔍 Testing: {name}")
    print(f"   Private Key: {private_key_hex}")
    
    if test_private_key(private_key_hex):
        print(f"   🎯 TREASURE FOUND! {name}")
        return True
    else:
        print(f"   ❌ No match")
        return False

def analyze_dogecoin_address():
    """Analyze the Dogecoin genesis address"""
    print("🐕 DOGECOIN GENESIS ADDRESS ANALYSIS")
    print("=" * 50)
    
    print(f"Address: {DOGECOIN_GENESIS_ADDRESS}")
    
    # Decode the address
    try:
        decoded = base58.b58decode(DOGECOIN_GENESIS_ADDRESS)
        print(f"Decoded bytes: {decoded.hex()}")
        
        # Extract components
        version = decoded[0]
        hash160 = decoded[1:21]
        checksum = decoded[21:25]
        
        print(f"Version byte: 0x{version:02x} ({version})")
        print(f"Hash160: {hash160.hex()}")
        print(f"Checksum: {checksum.hex()}")
        
        # Verify checksum
        payload = decoded[:-4]
        calculated_checksum = hashlib.sha256(hashlib.sha256(payload).digest()).digest()[:4]
        checksum_valid = checksum == calculated_checksum
        print(f"Checksum valid: {checksum_valid}")
        
        return hash160.hex()
        
    except Exception as e:
        print(f"Error decoding address: {e}")
        return None

def analyze_scriptsig():
    """Analyze the Bellscoin scriptsig"""
    print("\n📜 BELLSCOIN SCRIPTSIG ANALYSIS")
    print("=" * 40)
    
    print(f"Full scriptsig: {BELLSCOIN_SCRIPTSIG}")
    print(f"Nintondo hex: {NINTONDO_HEX}")
    print(f"Nintondo text: {bytes.fromhex(NINTONDO_HEX).decode()}")
    
    # Break down the scriptsig
    print(f"\nScriptsig breakdown:")
    print(f"  04 = OP_PUSHBYTES_4")
    print(f"  ffff001d = nBits value")
    print(f"  01 = OP_PUSHBYTES_1") 
    print(f"  04 = Extra nonce")
    print(f"  08 = OP_PUSHBYTES_8")
    print(f"  4e696e746f6e646f = 'Nintondo'")
    
    # Extract components for analysis
    nbits = "ffff001d"
    extra_nonce = "04"
    
    return nbits, extra_nonce

def generate_comprehensive_candidates() -> List[Tuple[str, str]]:
    """Generate candidates using all available information"""
    candidates = []
    
    # Get Dogecoin address hash160
    doge_hash160 = analyze_dogecoin_address()
    
    # Get scriptsig components
    nbits, extra_nonce = analyze_scriptsig()
    
    if doge_hash160:
        # Use Dogecoin address hash160
        candidates.extend([
            ("Dogecoin Hash160", doge_hash160.ljust(64, '0')),
            ("SHA256(Dogecoin Hash160)", hashlib.sha256(doge_hash160.encode()).hexdigest()),
            ("SHA256('Nintondo' + Doge Hash160)", hashlib.sha256(f"Nintondo{doge_hash160}".encode()).hexdigest()),
        ])
    
    # Nintondo-based candidates
    candidates.extend([
        ("Nintondo hex", NINTONDO_HEX.ljust(64, '0')),
        ("SHA256('Nintondo')", hashlib.sha256("Nintondo".encode()).hexdigest()),
        ("SHA256(Nintondo hex)", hashlib.sha256(NINTONDO_HEX.encode()).hexdigest()),
        ("SHA256(Nintondo bytes)", hashlib.sha256(bytes.fromhex(NINTONDO_HEX)).hexdigest()),
    ])
    
    # Scriptsig-based candidates
    candidates.extend([
        ("Full scriptsig", BELLSCOIN_SCRIPTSIG.ljust(64, '0')),
        ("SHA256(Full scriptsig)", hashlib.sha256(BELLSCOIN_SCRIPTSIG.encode()).hexdigest()),
        ("SHA256(scriptsig bytes)", hashlib.sha256(bytes.fromhex(BELLSCOIN_SCRIPTSIG)).hexdigest()),
        ("NBits value", nbits.ljust(64, '0')),
        ("SHA256(NBits)", hashlib.sha256(nbits.encode()).hexdigest()),
    ])
    
    # Combine our private key with new information
    candidates.extend([
        ("Our private key", PRIVATE_KEY),
        ("SHA256(Private key + 'Nintondo')", hashlib.sha256(f"{PRIVATE_KEY}Nintondo".encode()).hexdigest()),
        ("SHA256('Nintondo' + Private key)", hashlib.sha256(f"Nintondo{PRIVATE_KEY}".encode()).hexdigest()),
    ])
    
    if doge_hash160:
        candidates.extend([
            ("SHA256(Private key + Doge Hash160)", hashlib.sha256(f"{PRIVATE_KEY}{doge_hash160}".encode()).hexdigest()),
            ("Private key XOR Doge Hash160", hex(int(PRIVATE_KEY, 16) ^ int(doge_hash160.ljust(64, '0'), 16))[2:].zfill(64)),
        ])
    
    # Address-based derivations
    candidates.extend([
        ("SHA256(Dogecoin Address)", hashlib.sha256(DOGECOIN_GENESIS_ADDRESS.encode()).hexdigest()),
        ("SHA256('Nintondo' + Doge Address)", hashlib.sha256(f"Nintondo{DOGECOIN_GENESIS_ADDRESS}".encode()).hexdigest()),
    ])
    
    # Combine all hex strings
    all_hex_data = [PRIVATE_KEY, NINTONDO_HEX, BELLSCOIN_SCRIPTSIG]
    if doge_hash160:
        all_hex_data.append(doge_hash160)
    
    combined_hex = ''.join(all_hex_data)
    candidates.extend([
        ("SHA256(All hex combined)", hashlib.sha256(combined_hex.encode()).hexdigest()),
        ("SHA256(All hex as bytes)", hashlib.sha256(bytes.fromhex(combined_hex.replace('Nintondo', NINTONDO_HEX))).hexdigest()),
    ])
    
    # Try variations of "Nintondo"
    nintondo_variations = [
        "nintondo", "NINTONDO", "NiNtOnDo", "Nintendo", "nintendo", "NINTENDO"
    ]
    
    for variation in nintondo_variations:
        candidates.extend([
            (f"SHA256('{variation}')", hashlib.sha256(variation.encode()).hexdigest()),
            (f"SHA256('{variation}' + Private key)", hashlib.sha256(f"{variation}{PRIVATE_KEY}".encode()).hexdigest()),
        ])
    
    # Mathematical operations with address components
    if doge_hash160:
        try:
            private_int = int(PRIVATE_KEY, 16)
            doge_int = int(doge_hash160, 16)
            nintondo_int = int(NINTONDO_HEX, 16)
            
            candidates.extend([
                ("Private + Doge + Nintondo", hex((private_int + doge_int + nintondo_int) % (2**256))[2:].zfill(64)),
                ("Private XOR Doge XOR Nintondo", hex(private_int ^ doge_int ^ nintondo_int)[2:].zfill(64)),
            ])
        except:
            pass
    
    return candidates

def main():
    """Main execution function"""
    print("🏴‍☠️ COMPREHENSIVE TREASURE HUNT ANALYSIS")
    print("=" * 70)
    print(f"Target Public Key: {TARGET_PUBKEY}")
    print(f"Our Private Key: {PRIVATE_KEY}")
    print(f"Dogecoin Genesis Address: {DOGECOIN_GENESIS_ADDRESS}")
    print(f"Nintondo: {NINTONDO_HEX} = '{bytes.fromhex(NINTONDO_HEX).decode()}'")
    print("=" * 70)
    
    # Generate and test candidates
    candidates = generate_comprehensive_candidates()
    total_candidates = len(candidates)
    
    print(f"\n🧪 TESTING {total_candidates} COMPREHENSIVE CANDIDATES")
    print("=" * 60)
    
    for i, (name, private_key_hex) in enumerate(candidates, 1):
        print(f"\n[{i}/{total_candidates}] ", end="")
        
        if test_candidate(name, private_key_hex):
            print(f"\n🎉 SUCCESS! The treasure has been found!")
            print(f"🎉 Method: {name}")
            print(f"🎉 Private key: {private_key_hex}")
            print(f"🎉 This key can unlock the 88 BEL from the genesis block!")
            return private_key_hex
    
    print(f"\n❌ No matches found in {total_candidates} comprehensive candidates")
    print("\n💡 Key insights:")
    print("- The 88 BEL are UNSPENT, meaning the private key has never been used")
    print("- 'Nintondo' is embedded in the scriptsig")
    print("- Our private key contains multiple timestamps")
    print("- The Dogecoin genesis address might hold additional clues")
    
    return None

if __name__ == "__main__":
    result = main()
    if result:
        print(f"\n🏆 FINAL RESULT: {result}")
