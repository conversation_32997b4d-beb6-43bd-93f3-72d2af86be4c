#!/usr/bin/env python3
"""
SHARED KEY TREASURE HUNT
========================

Since the same public key is used in BOTH Bellscoin and Dogecoin,
this suggests the private key might be derived from something common
to both projects or intentionally simple/memorable.

Target Genesis Public Key: 040184710fa689ad5023690c80f3a49c8f13f8d45b8c857fbcbc8bc4a8e4d3eb4b10f4d4604fa08dce601aaf0f470216fe1b51850b4acf21b179c45070ac7b03a9
"""

import hashlib
import itertools
from ecdsa import Signing<PERSON>ey, SECP256k1

# Target genesis public key (shared between Bellscoin and Dogecoin)
TARGET_PUBKEY = "040184710fa689ad5023690c80f3a49c8f13f8d45b8c857fbcbc8bc4a8e4d3eb4b10f4d4604fa08dce601aaf0f470216fe1b51850b4acf21b179c45070ac7b03a9"

def sha256_hash(text: str) -> str:
    """Generate SHA256 hash of text"""
    return hashlib.sha256(text.encode()).hexdigest()

def test_private_key(private_key_hex: str) -> bool:
    """Test if a hex string generates the target public key"""
    try:
        if private_key_hex.startswith('0x'):
            private_key_hex = private_key_hex[2:]
        
        if len(private_key_hex) != 64:
            return False
            
        private_key_int = int(private_key_hex, 16)
        
        if private_key_int <= 0 or private_key_int >= 0xFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFEBAAEDCE6AF48A03BBFD25E8CD0364141:
            return False
        
        sk = SigningKey.from_secret_exponent(private_key_int, curve=SECP256k1)
        vk = sk.get_verifying_key()
        
        public_key_bytes = b'\x04' + vk.to_string()
        public_key_hex = public_key_bytes.hex()
        
        return public_key_hex.lower() == TARGET_PUBKEY.lower()
        
    except Exception as e:
        return False

def test_shared_concepts():
    """Test concepts that would be common to both projects"""
    print("🤝 TESTING SHARED CONCEPTS")
    print("=" * 50)
    
    # Concepts that would be shared between both projects
    shared_concepts = [
        # Project combinations
        "BellscoinDogecoin",
        "DogecoinBellscoin", 
        "BellsDoge",
        "DogeBells",
        
        # Common crypto terms
        "cryptocurrency",
        "blockchain",
        "genesis",
        "satoshi",
        "bitcoin",
        "litecoin",
        "scrypt",
        
        # 2013 related (year both were created)
        "2013",
        "twentythirteen",
        "year2013",
        
        # Developer related
        "developer",
        "coder",
        "programmer",
        "test",
        "testing",
        "debug",
        
        # Simple memorable phrases
        "password",
        "secret",
        "private",
        "key",
        "treasure",
        "hidden",
        "shared",
        "common",
    ]
    
    for concept in shared_concepts:
        print(f"Testing shared concept: '{concept}'")
        
        # Test direct hash
        hash_result = sha256_hash(concept)
        if test_private_key(hash_result):
            print(f"🎯 TREASURE FOUND! Shared concept '{concept}': {hash_result}")
            return hash_result
        
        # Test with Nintondo
        nintondo_combo = sha256_hash("Nintondo" + concept)
        if test_private_key(nintondo_combo):
            print(f"🎯 TREASURE FOUND! Nintondo+'{concept}': {nintondo_combo}")
            return nintondo_combo
        
        # Test concept + Nintondo
        concept_nintondo = sha256_hash(concept + "Nintondo")
        if test_private_key(concept_nintondo):
            print(f"🎯 TREASURE FOUND! '{concept}'+Nintondo: {concept_nintondo}")
            return concept_nintondo
    
    return None

def test_developer_names():
    """Test common developer names and handles"""
    print("\n👨‍💻 TESTING DEVELOPER NAMES")
    print("=" * 50)
    
    # Common developer names/handles from crypto space circa 2013
    dev_names = [
        "satoshi",
        "satoshinakamoto", 
        "nakamoto",
        "coblee",  # Charlie Lee (Litecoin)
        "charlie",
        "jackson",  # Jackson Palmer (Dogecoin)
        "palmer",
        "billy",    # Billy Markus (Dogecoin)
        "markus",
        "shibetoshi",
        "admin",
        "root",
        "user",
        "test",
        "demo",
    ]
    
    for name in dev_names:
        print(f"Testing dev name: '{name}'")
        
        hash_result = sha256_hash(name)
        if test_private_key(hash_result):
            print(f"🎯 TREASURE FOUND! Dev name '{name}': {hash_result}")
            return hash_result
        
        # With Nintondo
        nintondo_combo = sha256_hash("Nintondo" + name)
        if test_private_key(nintondo_combo):
            print(f"🎯 TREASURE FOUND! Nintondo+'{name}': {nintondo_combo}")
            return nintondo_combo
    
    return None

def test_simple_sequences():
    """Test very simple, memorable sequences"""
    print("\n🔢 TESTING SIMPLE SEQUENCES")
    print("=" * 50)
    
    # Simple sequences that a developer might use for testing
    sequences = [
        "1234567890",
        "0123456789",
        "abcdefghijklmnopqrstuvwxyz",
        "ABCDEFGHIJKLMNOPQRSTUVWXYZ",
        "qwertyuiop",
        "asdfghjkl",
        "zxcvbnm",
        "hello",
        "world",
        "helloworld",
        "test123",
        "password123",
        "admin123",
        "secret123",
        "12345678",
        "87654321",
        "11111111",
        "00000000",
        "ffffffff",
        "deadbeef",
        "cafebabe",
        "feedface",
        "badcafe",
    ]
    
    for seq in sequences:
        print(f"Testing sequence: '{seq}'")
        
        hash_result = sha256_hash(seq)
        if test_private_key(hash_result):
            print(f"🎯 TREASURE FOUND! Sequence '{seq}': {hash_result}")
            return hash_result
        
        # With Nintondo
        nintondo_combo = sha256_hash("Nintondo" + seq)
        if test_private_key(nintondo_combo):
            print(f"🎯 TREASURE FOUND! Nintondo+'{seq}': {nintondo_combo}")
            return nintondo_combo
    
    return None

def test_date_combinations():
    """Test date combinations that would be meaningful in 2013"""
    print("\n📅 TESTING DATE COMBINATIONS")
    print("=" * 50)
    
    # Important dates in crypto history around 2013
    dates = [
        "20131203",  # Dogecoin launch
        "20131206",  # Dogecoin official launch
        "20130101",  # New Year 2013
        "20131231",  # End of 2013
        "20090103",  # Bitcoin genesis block
        "20111007",  # Litecoin launch
        "01012013",  # Alternative date format
        "03122013",  # Dogecoin launch alt format
        "06122013",  # Dogecoin official alt format
    ]
    
    for date in dates:
        print(f"Testing date: '{date}'")
        
        hash_result = sha256_hash(date)
        if test_private_key(hash_result):
            print(f"🎯 TREASURE FOUND! Date '{date}': {hash_result}")
            return hash_result
        
        # With Nintondo
        nintondo_combo = sha256_hash("Nintondo" + date)
        if test_private_key(nintondo_combo):
            print(f"🎯 TREASURE FOUND! Nintondo+'{date}': {nintondo_combo}")
            return nintondo_combo
        
        # Date + Nintondo
        date_nintondo = sha256_hash(date + "Nintondo")
        if test_private_key(date_nintondo):
            print(f"🎯 TREASURE FOUND! '{date}'+Nintondo: {date_nintondo}")
            return date_nintondo
    
    return None

def test_meme_references():
    """Test meme and internet culture references from 2013"""
    print("\n🐕 TESTING MEME REFERENCES")
    print("=" * 50)
    
    # Memes and internet culture from 2013 era
    memes = [
        "doge",
        "wow",
        "such",
        "much",
        "very",
        "so",
        "amaze",
        "shibe",
        "shibainu",
        "moon",
        "tomoon",
        "tothemoon",
        "hodl",
        "lambo",
        "when",
        "wen",
        "wenmoon",
        "wenlambo",
    ]
    
    for meme in memes:
        print(f"Testing meme: '{meme}'")
        
        hash_result = sha256_hash(meme)
        if test_private_key(hash_result):
            print(f"🎯 TREASURE FOUND! Meme '{meme}': {hash_result}")
            return hash_result
        
        # With Nintondo
        nintondo_combo = sha256_hash("Nintondo" + meme)
        if test_private_key(nintondo_combo):
            print(f"🎯 TREASURE FOUND! Nintondo+'{meme}': {nintondo_combo}")
            return nintondo_combo
    
    return None

def test_word_combinations():
    """Test combinations of important words"""
    print("\n🔗 TESTING WORD COMBINATIONS")
    print("=" * 50)
    
    # Key words from the treasure hunt
    key_words = ["Nintondo", "Bellscoin", "Dogecoin", "2013", "treasure", "key", "private", "secret"]
    
    # Test pairs of words
    for word1, word2 in itertools.combinations(key_words, 2):
        combo1 = word1 + word2
        combo2 = word2 + word1
        
        for combo in [combo1, combo2]:
            print(f"Testing combination: '{combo}'")
            
            hash_result = sha256_hash(combo)
            if test_private_key(hash_result):
                print(f"🎯 TREASURE FOUND! Combination '{combo}': {hash_result}")
                return hash_result
    
    return None

def main():
    """Main execution function"""
    print("🏴‍☠️ SHARED KEY TREASURE HUNT")
    print("=" * 60)
    print("Focusing on the fact that this key is SHARED between Bellscoin and Dogecoin...")
    print(f"Target: {TARGET_PUBKEY[:32]}...")
    
    # Test different approaches focused on the shared nature
    approaches = [
        test_shared_concepts,
        test_developer_names,
        test_simple_sequences,
        test_date_combinations,
        test_meme_references,
        test_word_combinations,
    ]
    
    for approach_func in approaches:
        result = approach_func()
        if result:
            print(f"\n🏆 TREASURE DISCOVERED: {result}")
            return result
    
    print(f"\n💔 No treasure found in shared key analysis")
    print("🤔 The private key might require an even more specific approach...")

if __name__ == "__main__":
    main()
