#!/usr/bin/env python3
"""
Comprehensive Private Key Hunt for Bellscoin Genesis Block
==========================================================

This script systematically tests all possible private key candidates based on
the comprehensive analysis of the Bellscoin and Dogecoin codebases.

Target Genesis Public Key: 040184710fa689ad5023690c80f3a49c8f13f8d45b8c857fbcbc8bc4a8e4d3eb4b10f4d4604fa08dce601aaf0f470216fe1b51850b4acf21b179c45070ac7b03a9

Key insights:
- Both Bellscoin and Dogecoin share the same genesis public key
- Both use "Nintondo" as the timestamp message
- The 88 coin movements in both genesis blocks suggest a deliberate connection
"""

import hashlib
import binascii
import itertools
from typing import Optional, List, Tuple
from ecdsa import SigningKey, SECP256k1
from ecdsa.util import string_to_number, number_to_string

# Target genesis public key
TARGET_PUBKEY = "040184710fa689ad5023690c80f3a49c8f13f8d45b8c857fbcbc8bc4a8e4d3eb4b10f4d4604fa08dce601aaf0f470216fe1b51850b4acf21b179c45070ac7b03a9"

# Key constants from codebase analysis
BELLSCOIN_HASH = "e5be24df57c43a82d15c2f06bda961296948f8f8eb48501bed1efb929afe0698"
DOGECOIN_HASH = "1a91e3dace36e2be3bf030a65679fe821aa1d6ef92e7c9902eb318182c355691"

# Genesis block constants
GENESIS_TIMESTAMP_BELLS = 1383509530  # Bellscoin Nov 3, 2013
GENESIS_NONCE_BELLS = 44481
GENESIS_TIMESTAMP_DOGE = 1386325540   # Dogecoin Dec 6, 2013  
GENESIS_NONCE_DOGE = 99943
SCRIPT_CONSTANT = 486604799  # 0x1d00ffff
NBITS = 0x1e0ffff0
MERKLE_ROOT = "5b2a3f53f605d62c53e62932dac6925e3d74afa5a4b459745c36d42d0ed26a69"

# Message and other constants
TIMESTAMP_MESSAGE = "Nintondo"
COIN_VALUE = 88  # 88 coins in genesis transaction

def sha256_hash(data: str) -> str:
    """Generate SHA256 hash of string"""
    return hashlib.sha256(data.encode()).hexdigest()

def test_private_key(private_key_hex: str) -> bool:
    """Test if a private key generates the target public key"""
    try:
        # Ensure the private key is exactly 64 hex characters
        if len(private_key_hex) != 64:
            return False
            
        # Convert hex to integer
        private_key_int = int(private_key_hex, 16)
        
        # Check if it's in valid range for secp256k1
        if private_key_int <= 0 or private_key_int >= 0xFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFEBAAEDCE6AF48A03BBFD25E8CD0364141:
            return False
            
        # Create signing key
        signing_key = SigningKey.from_string(
            binascii.unhexlify(private_key_hex), 
            curve=SECP256k1
        )
        
        # Get uncompressed public key
        verifying_key = signing_key.get_verifying_key()
        public_key_bytes = verifying_key.to_string()
        
        # Format as uncompressed public key (04 + x + y coordinates)
        public_key_hex = "04" + public_key_bytes.hex()
        
        return public_key_hex.lower() == TARGET_PUBKEY.lower()
        
    except Exception as e:
        return False

def test_candidate(name: str, private_key_hex: str) -> bool:
    """Test a candidate and print results"""
    print(f"\nTesting: {name}")
    print(f"Private Key: {private_key_hex}")
    
    if test_private_key(private_key_hex):
        print(f"🎯 TREASURE FOUND! {name}")
        print(f"🎯 Private Key: {private_key_hex}")
        print(f"🎯 This generates the target genesis public key!")
        return True
    else:
        print("❌ No match")
        return False

def generate_candidates() -> List[Tuple[str, str]]:
    """Generate all possible private key candidates"""
    candidates = []
    
    # Direct hash candidates
    candidates.extend([
        ("SHA256('Nintondo')", sha256_hash("Nintondo")),
        ("SHA256('Nintendo')", sha256_hash("Nintendo")),
        ("Bellscoin Genesis Hash", BELLSCOIN_HASH),
        ("Dogecoin Genesis Hash", DOGECOIN_HASH),
    ])
    
    # Timestamp-based candidates
    candidates.extend([
        ("SHA256(Bellscoin Timestamp)", sha256_hash(str(GENESIS_TIMESTAMP_BELLS))),
        ("SHA256(Dogecoin Timestamp)", sha256_hash(str(GENESIS_TIMESTAMP_DOGE))),
        ("SHA256('Nintondo' + Bells Timestamp)", sha256_hash(f"Nintondo{GENESIS_TIMESTAMP_BELLS}")),
        ("SHA256('Nintondo' + Doge Timestamp)", sha256_hash(f"Nintondo{GENESIS_TIMESTAMP_DOGE}")),
    ])
    
    # Nonce-based candidates
    candidates.extend([
        ("SHA256(Bellscoin Nonce)", sha256_hash(str(GENESIS_NONCE_BELLS))),
        ("SHA256(Dogecoin Nonce)", sha256_hash(str(GENESIS_NONCE_DOGE))),
        ("SHA256('Nintondo' + Bells Nonce)", sha256_hash(f"Nintondo{GENESIS_NONCE_BELLS}")),
        ("SHA256('Nintondo' + Doge Nonce)", sha256_hash(f"Nintondo{GENESIS_NONCE_DOGE}")),
    ])
    
    # Combined timestamp + nonce
    candidates.extend([
        ("SHA256(Bells Timestamp + Nonce)", sha256_hash(f"{GENESIS_TIMESTAMP_BELLS}{GENESIS_NONCE_BELLS}")),
        ("SHA256(Doge Timestamp + Nonce)", sha256_hash(f"{GENESIS_TIMESTAMP_DOGE}{GENESIS_NONCE_DOGE}")),
        ("SHA256('Nintondo' + Bells Timestamp + Nonce)", sha256_hash(f"Nintondo{GENESIS_TIMESTAMP_BELLS}{GENESIS_NONCE_BELLS}")),
        ("SHA256('Nintondo' + Doge Timestamp + Nonce)", sha256_hash(f"Nintondo{GENESIS_TIMESTAMP_DOGE}{GENESIS_NONCE_DOGE}")),
    ])
    
    # Script constant combinations
    candidates.extend([
        ("SHA256(Script Constant)", sha256_hash(str(SCRIPT_CONSTANT))),
        ("SHA256('Nintondo' + Script Constant)", sha256_hash(f"Nintondo{SCRIPT_CONSTANT}")),
        ("SHA256(Script Constant + Bells Timestamp)", sha256_hash(f"{SCRIPT_CONSTANT}{GENESIS_TIMESTAMP_BELLS}")),
    ])
    
    # 88 coin value combinations
    candidates.extend([
        ("SHA256('Nintondo88')", sha256_hash("Nintondo88")),
        ("SHA256('88Nintondo')", sha256_hash("88Nintondo")),
        ("SHA256('88')", sha256_hash("88")),
    ])
    
    # Cross-blockchain combinations
    candidates.extend([
        ("SHA256(Both Hashes)", sha256_hash(BELLSCOIN_HASH + DOGECOIN_HASH)),
        ("SHA256(Both Timestamps)", sha256_hash(f"{GENESIS_TIMESTAMP_BELLS}{GENESIS_TIMESTAMP_DOGE}")),
        ("SHA256('NintondoBellscoinDogecoin')", sha256_hash("NintondoBellscoinDogecoin")),
        ("SHA256('DogecoinBellscoin')", sha256_hash("DogecoinBellscoin")),
    ])
    
    # Merkle root combinations
    candidates.extend([
        ("Merkle Root", MERKLE_ROOT),
        ("SHA256(Merkle Root)", sha256_hash(MERKLE_ROOT)),
        ("SHA256('Nintondo' + Merkle Root)", sha256_hash(f"Nintondo{MERKLE_ROOT}")),
    ])
    
    # Date-based combinations (2013 launch year)
    candidates.extend([
        ("SHA256('Nintondo2013')", sha256_hash("Nintondo2013")),
        ("SHA256('2013Nintondo')", sha256_hash("2013Nintondo")),
        ("SHA256('2013')", sha256_hash("2013")),
    ])
    
    return candidates

def main():
    """Main execution function"""
    print("🔍 Comprehensive Private Key Hunt for Bellscoin Genesis Block")
    print("=" * 70)
    print(f"Target Public Key: {TARGET_PUBKEY}")
    print("=" * 70)
    
    candidates = generate_candidates()
    total_candidates = len(candidates)
    
    print(f"\n📊 Testing {total_candidates} candidates...")
    
    for i, (name, private_key_hex) in enumerate(candidates, 1):
        print(f"\n[{i}/{total_candidates}] ", end="")
        
        if test_candidate(name, private_key_hex):
            print(f"\n🎉 SUCCESS! The treasure has been found!")
            print(f"🎉 Private key that unlocks Dogecoin's launchpad: {private_key_hex}")
            return private_key_hex
    
    print(f"\n❌ No matches found in {total_candidates} candidates")
    print("💡 The private key might require a more complex derivation method")
    return None

if __name__ == "__main__":
    result = main()
    if result:
        print(f"\n🏆 FINAL RESULT: {result}")
