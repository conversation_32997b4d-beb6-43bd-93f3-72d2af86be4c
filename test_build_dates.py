#!/usr/bin/env python3
"""
TEST BUILD DATES AND METADATA
=============================

Test dates and metadata found in build files and version information.

Target Genesis Public Key: 040184710fa689ad5023690c80f3a49c8f13f8d45b8c857fbcbc8bc4a8e4d3eb4b10f4d4604fa08dce601aaf0f470216fe1b51850b4acf21b179c45070ac7b03a9
"""

import hashlib
import datetime
from ecdsa import SigningKey, SECP256k1

# Target genesis public key
TARGET_PUBKEY = "040184710fa689ad5023690c80f3a49c8f13f8d45b8c857fbcbc8bc4a8e4d3eb4b10f4d4604fa08dce601aaf0f470216fe1b51850b4acf21b179c45070ac7b03a9"

def sha256_hash(text: str) -> str:
    """Generate SHA256 hash of text"""
    return hashlib.sha256(text.encode()).hexdigest()

def test_private_key(private_key_hex: str) -> bool:
    """Test if a hex string generates the target public key"""
    try:
        if private_key_hex.startswith('0x'):
            private_key_hex = private_key_hex[2:]
        
        if len(private_key_hex) != 64:
            return False
            
        private_key_int = int(private_key_hex, 16)
        
        if private_key_int <= 0 or private_key_int >= 0xFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFEBAAEDCE6AF48A03BBFD25E8CD0364141:
            return False
        
        sk = SigningKey.from_secret_exponent(private_key_int, curve=SECP256k1)
        vk = sk.get_verifying_key()
        
        public_key_bytes = b'\x04' + vk.to_string()
        public_key_hex = public_key_bytes.hex()
        
        return public_key_hex.lower() == TARGET_PUBKEY.lower()
        
    except Exception as e:
        return False

def test_build_dates():
    """Test build dates and related timestamps"""
    print("📅 TESTING BUILD DATES")
    print("=" * 40)
    
    # Build date from bellscoin/src/obj/build.h: "2013-05-26 22:13:27 -0700"
    build_date_str = "2013-05-26 22:13:27 -0700"
    
    # Parse the date
    try:
        # Remove timezone for parsing
        date_part = "2013-05-26 22:13:27"
        dt = datetime.datetime.strptime(date_part, "%Y-%m-%d %H:%M:%S")
        
        # Convert to timestamp
        build_timestamp = int(dt.timestamp())
        print(f"Build timestamp: {build_timestamp}")
        
        # Test various formats
        candidates = [
            ("Build date string", build_date_str),
            ("Build date (no timezone)", date_part),
            ("Build timestamp", str(build_timestamp)),
            ("Build year", "2013"),
            ("Build month-day", "0526"),
            ("Build time", "221327"),
            ("Build date compact", "20130526"),
            ("Build datetime compact", "20130526221327"),
        ]
        
        for name, candidate in candidates:
            print(f"\nTesting {name}: '{candidate}'")
            
            # Direct hash
            hash_result = sha256_hash(candidate)
            print(f"  SHA256: {hash_result}")
            if test_private_key(hash_result):
                print(f"🎯 TREASURE FOUND! {name}: {hash_result}")
                return hash_result
            
            # With Nintondo
            nintondo_combo = sha256_hash("Nintondo" + candidate)
            print(f"  Nintondo+: {nintondo_combo[:32]}...")
            if test_private_key(nintondo_combo):
                print(f"🎯 TREASURE FOUND! Nintondo+{name}: {nintondo_combo}")
                return nintondo_combo
            
            print(f"  ❌ No match")
        
    except Exception as e:
        print(f"Error parsing build date: {e}")
    
    return None

def test_version_info():
    """Test version information from various files"""
    print("\n🔢 TESTING VERSION INFORMATION")
    print("=" * 40)
    
    # Look for version info in files
    version_candidates = []
    
    # Check version.h and version.cpp
    try:
        with open('bellscoin/src/version.h', 'r') as f:
            content = f.read()
            
            # Look for version numbers
            import re
            version_patterns = [
                r'#define\s+CLIENT_VERSION_MAJOR\s+(\d+)',
                r'#define\s+CLIENT_VERSION_MINOR\s+(\d+)',
                r'#define\s+CLIENT_VERSION_REVISION\s+(\d+)',
                r'#define\s+CLIENT_VERSION_BUILD\s+(\d+)',
                r'(\d+\.\d+\.\d+)',
            ]
            
            for pattern in version_patterns:
                matches = re.findall(pattern, content)
                version_candidates.extend(matches)
                
    except FileNotFoundError:
        print("version.h not found")
    
    # Test version candidates
    for candidate in set(version_candidates):
        if candidate and len(candidate) >= 3:
            print(f"Testing version: {candidate}")
            
            hash_result = sha256_hash(candidate)
            if test_private_key(hash_result):
                print(f"🎯 TREASURE FOUND! Version '{candidate}': {hash_result}")
                return hash_result
            
            # With Nintondo
            nintondo_combo = sha256_hash("Nintondo" + candidate)
            if test_private_key(nintondo_combo):
                print(f"🎯 TREASURE FOUND! Nintondo+version '{candidate}': {nintondo_combo}")
                return nintondo_combo
    
    return None

def test_git_info():
    """Test git-related information"""
    print("\n🌿 TESTING GIT INFORMATION")
    print("=" * 40)
    
    # Common git-related strings
    git_candidates = [
        "v0.6.0",
        "v0.8.0", 
        "v1.0.0",
        "master",
        "main",
        "develop",
        "release",
        "59887e8",  # Example commit hash from genbuild.sh comment
    ]
    
    for candidate in git_candidates:
        print(f"Testing git info: {candidate}")
        
        hash_result = sha256_hash(candidate)
        if test_private_key(hash_result):
            print(f"🎯 TREASURE FOUND! Git info '{candidate}': {hash_result}")
            return hash_result
        
        # With Nintondo
        nintondo_combo = sha256_hash("Nintondo" + candidate)
        if test_private_key(nintondo_combo):
            print(f"🎯 TREASURE FOUND! Nintondo+git '{candidate}': {nintondo_combo}")
            return nintondo_combo
    
    return None

def test_compiler_flags():
    """Test compiler flags and build settings"""
    print("\n🔧 TESTING COMPILER FLAGS")
    print("=" * 40)
    
    # Flags from makefile.unix
    flags = [
        "USE_IPV6",
        "BOOST_SPIRIT_THREADSAFE",
        "TEST_DATA_DIR",
        "BOOST_TEST_DYN_LINK",
        "USE_UPNP",
        "HAVE_BUILD_INFO",
        "_FORTIFY_SOURCE",
        "fstack-protector-all",
        "fPIE",
        "O2",
    ]
    
    for flag in flags:
        print(f"Testing flag: {flag}")
        
        hash_result = sha256_hash(flag)
        if test_private_key(hash_result):
            print(f"🎯 TREASURE FOUND! Flag '{flag}': {hash_result}")
            return hash_result
        
        # With Nintondo
        nintondo_combo = sha256_hash("Nintondo" + flag)
        if test_private_key(nintondo_combo):
            print(f"🎯 TREASURE FOUND! Nintondo+flag '{flag}': {nintondo_combo}")
            return nintondo_combo
    
    return None

def test_file_paths():
    """Test file paths and directory names"""
    print("\n📁 TESTING FILE PATHS")
    print("=" * 40)
    
    # Important paths
    paths = [
        "bellscoin",
        "dogecoin", 
        "src",
        "obj",
        "test",
        "share",
        "qt",
        "main.cpp",
        "key.cpp",
        "util.cpp",
        "checkpoints.cpp",
        "build.h",
        "genbuild.sh",
    ]
    
    for path in paths:
        print(f"Testing path: {path}")
        
        hash_result = sha256_hash(path)
        if test_private_key(hash_result):
            print(f"🎯 TREASURE FOUND! Path '{path}': {hash_result}")
            return hash_result
        
        # With Nintondo
        nintondo_combo = sha256_hash("Nintondo" + path)
        if test_private_key(nintondo_combo):
            print(f"🎯 TREASURE FOUND! Nintondo+path '{path}': {nintondo_combo}")
            return nintondo_combo
    
    return None

def main():
    """Main execution function"""
    print("🏴‍☠️ TESTING BUILD DATES AND METADATA")
    print("=" * 60)
    print("Testing dates, versions, and build metadata...")
    print(f"Target: {TARGET_PUBKEY[:32]}...")
    
    # Test different metadata types
    tests = [
        test_build_dates,
        test_version_info,
        test_git_info,
        test_compiler_flags,
        test_file_paths,
    ]
    
    for test_func in tests:
        result = test_func()
        if result:
            print(f"\n🏆 TREASURE DISCOVERED: {result}")
            return result
    
    print(f"\n💔 No treasure found in build metadata")

if __name__ == "__main__":
    main()
