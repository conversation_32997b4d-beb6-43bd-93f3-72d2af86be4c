#!/usr/bin/env python3
"""
Test WIF Keys from Codebase
===========================

This script tests the WIF (Wallet Import Format) keys found in the test files
to see if any of them generate the target genesis public key.

Target Genesis Public Key: 040184710fa689ad5023690c80f3a49c8f13f8d45b8c857fbcbc8bc4a8e4d3eb4b10f4d4604fa08dce601aaf0f470216fe1b51850b4acf21b179c45070ac7b03a9
"""

import hashlib
import binascii
import base58
from typing import Optional, List, Tuple
from ecdsa import SigningKey, SECP256k1
from ecdsa.util import string_to_number, number_to_string

# Target genesis public key
TARGET_PUBKEY = "040184710fa689ad5023690c80f3a49c8f13f8d45b8c857fbcbc8bc4a8e4d3eb4b10f4d4604fa08dce601aaf0f470216fe1b51850b4acf21b179c45070ac7b03a9"

# WIF keys found in the test files
WIF_KEYS = [
    # From Bellscoin key_tests.cpp
    "6uu5bsZLA2Lm6yCxgwxDxHyZmhYeqBMLQT83Fyq738YhYucQPQf",  # strSecret1
    "6vZDRwYgTNidWzmKs9x8QzQGeWCqbdUtNRpEKZMaP67ZSn8XMjb",  # strSecret2
    "T6UsJv9hYpvDfM5noKYkB3vfeHxhyegkeWJ4y7qKeQJuyXMK11XX", # strSecret1C
    "T9PBs5kq9QrkBPxeGNWKitMi4XuFVr25jaXTnuopLVZxCUAJbixA", # strSecret2C
    
    # From Dogecoin key_tests.cpp (same keys)
    "6uu5bsZLA2Lm6yCxgwxDxHyZmhYeqBMLQT83Fyq738YhYucQPQf",
    "6vZDRwYgTNidWzmKs9x8QzQGeWCqbdUtNRpEKZMaP67ZSn8XMjb",
    "T6UsJv9hYpvDfM5noKYkB3vfeHxhyegkeWJ4y7qKeQJuyXMK11XX",
    "T9PBs5kq9QrkBPxeGNWKitMi4XuFVr25jaXTnuopLVZxCUAJbixA",
]

def decode_wif_to_private_key(wif_key: str) -> Optional[str]:
    """Decode a WIF key to get the private key in hex format"""
    try:
        # Decode base58
        decoded = base58.b58decode(wif_key)
        
        # Check length (should be 37 bytes for uncompressed, 38 for compressed)
        if len(decoded) not in [37, 38]:
            return None
            
        # Remove version byte (first byte) and checksum (last 4 bytes)
        private_key_bytes = decoded[1:-4]
        
        # If compressed flag is present, remove it
        if len(private_key_bytes) == 33:
            private_key_bytes = private_key_bytes[:-1]
            
        # Convert to hex
        return private_key_bytes.hex()
        
    except Exception as e:
        print(f"Error decoding WIF {wif_key}: {e}")
        return None

def test_private_key(private_key_hex: str) -> bool:
    """Test if a private key generates the target public key"""
    try:
        if len(private_key_hex) != 64:
            return False
            
        private_key_int = int(private_key_hex, 16)
        if private_key_int <= 0 or private_key_int >= 0xFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFEBAAEDCE6AF48A03BBFD25E8CD0364141:
            return False
            
        signing_key = SigningKey.from_string(binascii.unhexlify(private_key_hex), curve=SECP256k1)
        verifying_key = signing_key.get_verifying_key()
        public_key_bytes = verifying_key.to_string()
        public_key_hex = "04" + public_key_bytes.hex()
        
        return public_key_hex.lower() == TARGET_PUBKEY.lower()
        
    except Exception as e:
        print(f"Error testing private key {private_key_hex}: {e}")
        return False

def test_candidate(name: str, wif_key: str) -> bool:
    """Test a WIF candidate and print results"""
    print(f"\nTesting: {name}")
    print(f"WIF Key: {wif_key}")
    
    # Decode WIF to private key
    private_key_hex = decode_wif_to_private_key(wif_key)
    if not private_key_hex:
        print("❌ Failed to decode WIF key")
        return False
        
    print(f"Private Key: {private_key_hex}")
    
    if test_private_key(private_key_hex):
        print(f"🎯 TREASURE FOUND! {name}")
        print(f"🎯 WIF Key: {wif_key}")
        print(f"🎯 Private Key: {private_key_hex}")
        return True
    else:
        print("❌ No match")
        return False

def generate_variations_from_wif_keys() -> List[Tuple[str, str]]:
    """Generate variations based on the WIF keys found"""
    variations = []
    
    # First decode all WIF keys to get their private keys
    decoded_keys = []
    for wif_key in WIF_KEYS:
        private_key_hex = decode_wif_to_private_key(wif_key)
        if private_key_hex:
            decoded_keys.append(private_key_hex)
    
    # Remove duplicates
    unique_keys = list(set(decoded_keys))
    
    print(f"Found {len(unique_keys)} unique private keys from WIF decoding:")
    for i, key in enumerate(unique_keys, 1):
        print(f"  [{i}] {key}")
    
    # Test combinations and variations
    for i, key in enumerate(unique_keys):
        variations.extend([
            (f"WIF Decoded Key {i+1}", key),
            (f"SHA256(WIF Key {i+1})", hashlib.sha256(key.encode()).hexdigest()),
            (f"SHA256('Nintondo' + WIF Key {i+1})", hashlib.sha256(f"Nintondo{key}".encode()).hexdigest()),
        ])
    
    # Try XOR operations between keys
    if len(unique_keys) >= 2:
        try:
            key1_int = int(unique_keys[0], 16)
            key2_int = int(unique_keys[1], 16)
            xor_result = hex(key1_int ^ key2_int)[2:].zfill(64)
            variations.append(("WIF Key 1 XOR Key 2", xor_result))
        except:
            pass
    
    # Try arithmetic operations
    if len(unique_keys) >= 2:
        try:
            key1_int = int(unique_keys[0], 16)
            key2_int = int(unique_keys[1], 16)
            sum_result = hex((key1_int + key2_int) % (2**256))[2:].zfill(64)
            diff_result = hex((key1_int - key2_int) % (2**256))[2:].zfill(64)
            variations.extend([
                ("WIF Key 1 + Key 2", sum_result),
                ("WIF Key 1 - Key 2", diff_result),
            ])
        except:
            pass
    
    return variations

def main():
    """Main execution function"""
    print("🔑 Testing WIF Keys from Codebase")
    print("=" * 50)
    print(f"Target Public Key: {TARGET_PUBKEY}")
    print("=" * 50)
    
    # Test direct WIF keys
    print(f"\n📊 Testing {len(set(WIF_KEYS))} unique WIF keys...")
    
    unique_wif_keys = list(set(WIF_KEYS))
    for i, wif_key in enumerate(unique_wif_keys, 1):
        print(f"\n[{i}/{len(unique_wif_keys)}] ", end="")
        
        if test_candidate(f"WIF Key {i}", wif_key):
            print(f"\n🎉 SUCCESS! The treasure has been found!")
            return wif_key
    
    # Test variations
    print(f"\n📊 Testing variations based on WIF keys...")
    variations = generate_variations_from_wif_keys()
    
    for i, (name, private_key_hex) in enumerate(variations, 1):
        print(f"\n[{i}/{len(variations)}] Testing: {name}")
        print(f"Private Key: {private_key_hex}")
        
        if test_private_key(private_key_hex):
            print(f"🎯 TREASURE FOUND! {name}")
            print(f"🎯 Private Key: {private_key_hex}")
            return private_key_hex
        else:
            print("❌ No match")
    
    print(f"\n❌ No matches found in WIF keys or their variations")
    print("💡 The private key might be derived using a different method")
    return None

if __name__ == "__main__":
    result = main()
    if result:
        print(f"\n🏆 FINAL RESULT: {result}")
