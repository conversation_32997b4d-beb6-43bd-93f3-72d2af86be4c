#!/usr/bin/env python3
"""
CORRECT BELLSCOIN ADDRESS GENERATION
====================================

Generate the correct Bellscoin address from the target public key.
Bellscoin addresses start with "B", so we need to find the correct version byte.

Target Genesis Public Key: 040184710fa689ad5023690c80f3a49c8f13f8d45b8c857fbcbc8bc4a8e4d3eb4b10f4d4604fa08dce601aaf0f470216fe1b51850b4acf21b179c45070ac7b03a9
"""

import hashlib
import base58

# Target genesis public key
TARGET_PUBKEY = "040184710fa689ad5023690c80f3a49c8f13f8d45b8c857fbcbc8bc4a8e4d3eb4b10f4d4604fa08dce601aaf0f470216fe1b51850b4acf21b179c45070ac7b03a9"

def sha256_hash(data):
    """SHA256 hash"""
    return hashlib.sha256(data).digest()

def ripemd160_hash(data):
    """RIPEMD160 hash"""
    return hashlib.new('ripemd160', data).digest()

def hash160(data):
    """Bitcoin-style hash160 (SHA256 then RIPEMD160)"""
    return ripemd160_hash(sha256_hash(data))

def pubkey_to_address(pubkey_hex, version_byte):
    """Convert public key to address with specific version byte"""
    try:
        # Convert hex to bytes
        pubkey_bytes = bytes.fromhex(pubkey_hex)
        
        # Hash160
        hash160_result = hash160(pubkey_bytes)
        
        # Add version byte
        versioned_hash = bytes([version_byte]) + hash160_result
        
        # Double SHA256 for checksum
        checksum = sha256_hash(sha256_hash(versioned_hash))[:4]
        
        # Combine and encode
        full_address = versioned_hash + checksum
        address = base58.b58encode(full_address).decode()
        
        return address
    except Exception as e:
        print(f"Error converting pubkey to address: {e}")
        return None

def find_bellscoin_version_byte():
    """Find the correct version byte for Bellscoin addresses that start with 'B'"""
    print("🔍 FINDING CORRECT BELLSCOIN VERSION BYTE")
    print("=" * 60)
    
    print(f"Target public key: {TARGET_PUBKEY}")
    print(f"Example Bellscoin address: BEGjru7J3ifXSZj4jYoZbugBdBAghFNxKy")
    print()
    
    # Test different version bytes to find which one produces addresses starting with 'B'
    for version_byte in range(0, 256):
        address = pubkey_to_address(TARGET_PUBKEY, version_byte)
        if address and address.startswith('B'):
            print(f"Version byte 0x{version_byte:02x} ({version_byte:3d}) produces: {address}")
            
            # Check if this looks like a valid Bellscoin address
            if len(address) >= 26 and len(address) <= 35:  # Typical address length
                print(f"  ✅ This looks like a valid Bellscoin address!")
                return version_byte, address
    
    print("❌ No version byte found that produces addresses starting with 'B'")
    return None, None

def test_known_version_bytes():
    """Test some known version bytes for various cryptocurrencies"""
    print("\n📋 TESTING KNOWN VERSION BYTES")
    print("=" * 60)
    
    known_version_bytes = [
        (0x00, "Bitcoin"),
        (0x05, "Bitcoin Script"),
        (0x1e, "Dogecoin"),
        (0x16, "Dogecoin Script"),
        (0x30, "Litecoin"),
        (0x32, "Litecoin Script"),
        (0x6f, "Bitcoin Testnet"),
        (0xc4, "Dogecoin Testnet"),
        (0x19, "Bellscoin candidate 1"),  # 25 in decimal
        (0x1a, "Bellscoin candidate 2"),  # 26 in decimal
        (0x1b, "Bellscoin candidate 3"),  # 27 in decimal
        (0x1c, "Bellscoin candidate 4"),  # 28 in decimal
        (0x1d, "Bellscoin candidate 5"),  # 29 in decimal
        (0x1f, "Bellscoin candidate 6"),  # 31 in decimal
        (0x20, "Bellscoin candidate 7"),  # 32 in decimal
        (0x21, "Bellscoin candidate 8"),  # 33 in decimal
        (0x22, "Bellscoin candidate 9"),  # 34 in decimal
        (0x23, "Bellscoin candidate 10"), # 35 in decimal
        (0x24, "Bellscoin candidate 11"), # 36 in decimal
        (0x25, "Bellscoin candidate 12"), # 37 in decimal
    ]
    
    for version_byte, name in known_version_bytes:
        address = pubkey_to_address(TARGET_PUBKEY, version_byte)
        print(f"{name:25} (0x{version_byte:02x}): {address}")
        
        if address and address.startswith('B'):
            print(f"  🎯 FOUND! This produces a Bellscoin-style address!")

def analyze_example_address():
    """Analyze the example Bellscoin address to understand its structure"""
    print("\n🔬 ANALYZING EXAMPLE BELLSCOIN ADDRESS")
    print("=" * 60)
    
    example_address = "BEGjru7J3ifXSZj4jYoZbugBdBAghFNxKy"
    print(f"Example address: {example_address}")
    
    try:
        # Decode the address
        decoded = base58.b58decode(example_address)
        print(f"Decoded length: {len(decoded)} bytes")
        print(f"Decoded hex: {decoded.hex()}")
        
        if len(decoded) >= 25:  # Standard address length
            version_byte = decoded[0]
            hash160_part = decoded[1:21]
            checksum = decoded[21:25]
            
            print(f"Version byte: 0x{version_byte:02x} ({version_byte})")
            print(f"Hash160: {hash160_part.hex()}")
            print(f"Checksum: {checksum.hex()}")
            
            # Verify checksum
            versioned_hash = decoded[:21]
            calculated_checksum = sha256_hash(sha256_hash(versioned_hash))[:4]
            
            if checksum == calculated_checksum:
                print("✅ Checksum is valid!")
                print(f"Bellscoin version byte is: 0x{version_byte:02x} ({version_byte})")
                
                # Now generate our target address with this version byte
                target_address = pubkey_to_address(TARGET_PUBKEY, version_byte)
                print(f"\n🎯 TARGET BELLSCOIN ADDRESS: {target_address}")
                return version_byte, target_address
            else:
                print("❌ Checksum is invalid!")
        
    except Exception as e:
        print(f"Error analyzing address: {e}")
    
    return None, None

def main():
    """Main execution function"""
    print("🏴‍☠️ CORRECT BELLSCOIN ADDRESS GENERATION")
    print("=" * 60)
    print("Finding the correct Bellscoin address format...")
    
    # First, analyze the example address to find the version byte
    version_byte, target_address = analyze_example_address()
    
    if version_byte is not None:
        print(f"\n✅ SUCCESS!")
        print(f"Bellscoin version byte: 0x{version_byte:02x} ({version_byte})")
        print(f"Target Bellscoin address: {target_address}")
        
        # Also show the Dogecoin address for comparison
        dogecoin_address = pubkey_to_address(TARGET_PUBKEY, 0x1e)
        print(f"Target Dogecoin address: {dogecoin_address}")
        
        return target_address
    else:
        # Fallback: try to find the version byte by brute force
        print("\n🔍 Fallback: Searching for version byte...")
        version_byte, target_address = find_bellscoin_version_byte()
        
        if target_address:
            print(f"\n✅ FOUND!")
            print(f"Bellscoin version byte: 0x{version_byte:02x} ({version_byte})")
            print(f"Target Bellscoin address: {target_address}")
            return target_address
        else:
            print("\n❌ Could not determine correct Bellscoin address format")
            
            # Show some candidates anyway
            test_known_version_bytes()
    
    return None

if __name__ == "__main__":
    main()
