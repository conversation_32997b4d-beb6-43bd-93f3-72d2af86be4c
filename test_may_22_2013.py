#!/usr/bin/env python3
"""
TEST MAY 22, 2013 DATE
======================

The decoded comment mentions "May 22, 2013" specifically.
This could be the key to the treasure!

Target Genesis Public Key: 040184710fa689ad5023690c80f3a49c8f13f8d45b8c857fbcbc8bc4a8e4d3eb4b10f4d4604fa08dce601aaf0f470216fe1b51850b4acf21b179c45070ac7b03a9
"""

import hashlib
import datetime
from ecdsa import SigningKey, SECP256k1

# Target genesis public key
TARGET_PUBKEY = "040184710fa689ad5023690c80f3a49c8f13f8d45b8c857fbcbc8bc4a8e4d3eb4b10f4d4604fa08dce601aaf0f470216fe1b51850b4acf21b179c45070ac7b03a9"

def sha256_hash(text: str) -> str:
    """Generate SHA256 hash of text"""
    return hashlib.sha256(text.encode()).hexdigest()

def test_private_key(private_key_hex: str) -> bool:
    """Test if a hex string generates the target public key"""
    try:
        if private_key_hex.startswith('0x'):
            private_key_hex = private_key_hex[2:]
        
        if len(private_key_hex) != 64:
            return False
            
        private_key_int = int(private_key_hex, 16)
        
        if private_key_int <= 0 or private_key_int >= 0xFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFEBAAEDCE6AF48A03BBFD25E8CD0364141:
            return False
        
        sk = SigningKey.from_secret_exponent(private_key_int, curve=SECP256k1)
        vk = sk.get_verifying_key()
        
        public_key_bytes = b'\x04' + vk.to_string()
        public_key_hex = public_key_bytes.hex()
        
        return public_key_hex.lower() == TARGET_PUBKEY.lower()
        
    except Exception as e:
        return False

def test_may_22_2013_variations():
    """Test all possible variations of May 22, 2013"""
    print("📅 TESTING MAY 22, 2013 VARIATIONS")
    print("=" * 50)
    
    # All possible date formats for May 22, 2013
    date_variations = [
        # Basic formats
        "May 22, 2013",
        "May 22 2013",
        "May22,2013",
        "May222013",
        "may 22, 2013",
        "may 22 2013",
        "may22,2013",
        "may222013",
        "MAY 22, 2013",
        "MAY 22 2013",
        "MAY22,2013",
        "MAY222013",
        
        # Numeric formats
        "05/22/2013",
        "05-22-2013",
        "05.22.2013",
        "05222013",
        "5/22/2013",
        "5-22-2013",
        "5.22.2013",
        "5222013",
        "22/05/2013",
        "22-05-2013",
        "22.05.2013",
        "22052013",
        "2013/05/22",
        "2013-05-22",
        "2013.05.22",
        "20130522",
        
        # With day names (it was a Wednesday)
        "Wednesday May 22, 2013",
        "Wed May 22, 2013",
        "Wednesday, May 22, 2013",
        "Wed, May 22, 2013",
        "May 22, 2013 Wednesday",
        "May 22, 2013 Wed",
        
        # ISO format
        "2013-05-22",
        "2013/05/22",
        "2013.05.22",
        
        # Timestamp formats (May 22, 2013 was Unix timestamp around 1369180800)
        "1369180800",  # Approximate timestamp for May 22, 2013 00:00 UTC
        "1369267200",  # May 22, 2013 24:00 UTC
        
        # Japanese format (since it mentions Japan)
        "2013年5月22日",
        "平成25年5月22日",
        
        # Short forms
        "522013",
        "52213",
        "22513",
        "225",
        "522",
    ]
    
    for date_var in date_variations:
        print(f"Testing date variation: '{date_var}'")
        
        # Test direct hash
        hash_result = sha256_hash(date_var)
        if test_private_key(hash_result):
            print(f"🎯 TREASURE FOUND! Date '{date_var}': {hash_result}")
            return hash_result
        
        # Test with Nintondo prefix
        nintondo_combo = sha256_hash("Nintondo" + date_var)
        if test_private_key(nintondo_combo):
            print(f"🎯 TREASURE FOUND! Nintondo+'{date_var}': {nintondo_combo}")
            return nintondo_combo
        
        # Test with Nintondo suffix
        date_nintondo = sha256_hash(date_var + "Nintondo")
        if test_private_key(date_nintondo):
            print(f"🎯 TREASURE FOUND! '{date_var}'+Nintondo: {date_nintondo}")
            return date_nintondo
    
    return None

def test_nikkei_may_22_combinations():
    """Test combinations with Nikkei and May 22, 2013"""
    print("\n📈 TESTING NIKKEI + MAY 22 COMBINATIONS")
    print("=" * 50)
    
    base_terms = ["Nikkei", "NIK", "Japan", "Stock", "Average"]
    date_terms = ["May 22, 2013", "20130522", "May222013", "522013"]
    
    for base in base_terms:
        for date in date_terms:
            combinations = [
                base + date,
                date + base,
                base + " " + date,
                date + " " + base,
            ]
            
            for combo in combinations:
                print(f"Testing Nikkei combo: '{combo}'")
                
                hash_result = sha256_hash(combo)
                if test_private_key(hash_result):
                    print(f"🎯 TREASURE FOUND! Nikkei combo '{combo}': {hash_result}")
                    return hash_result
                
                # With Nintondo
                nintondo_combo = sha256_hash("Nintondo" + combo)
                if test_private_key(nintondo_combo):
                    print(f"🎯 TREASURE FOUND! Nintondo+'{combo}': {nintondo_combo}")
                    return nintondo_combo
    
    return None

def test_time_combinations():
    """Test time combinations from the comment (12:16 a.m. EDT)"""
    print("\n🕐 TESTING TIME COMBINATIONS")
    print("=" * 50)
    
    time_variations = [
        "12:16 a.m.",
        "12:16 am",
        "12:16am",
        "1216am",
        "1216",
        "00:16",
        "0016",
        "12:16 a.m. EDT",
        "12:16am EDT",
        "1216am EDT",
        "1216 EDT",
    ]
    
    date_base = "May 22, 2013"
    
    for time_var in time_variations:
        combinations = [
            time_var,
            date_base + " " + time_var,
            time_var + " " + date_base,
            "Nintondo" + time_var,
            time_var + "Nintondo",
        ]
        
        for combo in combinations:
            print(f"Testing time combo: '{combo}'")
            
            hash_result = sha256_hash(combo)
            if test_private_key(hash_result):
                print(f"🎯 TREASURE FOUND! Time combo '{combo}': {hash_result}")
                return hash_result
    
    return None

def test_percentage_combinations():
    """Test percentage combinations from the comment (+1.77%, +1.2%)"""
    print("\n📊 TESTING PERCENTAGE COMBINATIONS")
    print("=" * 50)
    
    percentages = [
        "1.77%",
        "1.2%",
        "+1.77%",
        "+1.2%",
        "177",
        "12",
        "1.77",
        "1.2",
        "177%",
        "12%",
    ]
    
    for pct in percentages:
        combinations = [
            pct,
            "Nintondo" + pct,
            pct + "Nintondo",
            "May 22, 2013" + pct,
            pct + "May 22, 2013",
            "Nikkei" + pct,
            pct + "Nikkei",
        ]
        
        for combo in combinations:
            print(f"Testing percentage combo: '{combo}'")
            
            hash_result = sha256_hash(combo)
            if test_private_key(hash_result):
                print(f"🎯 TREASURE FOUND! Percentage combo '{combo}': {hash_result}")
                return hash_result
    
    return None

def test_full_sentence_variations():
    """Test variations of the full sentence"""
    print("\n📝 TESTING FULL SENTENCE VARIATIONS")
    print("=" * 50)
    
    # Key phrases from the decoded text
    phrases = [
        "Japan's Nikkei Stock Average",
        "Japans Nikkei Stock Average",
        "JP:NIK +1.77%",
        "JP:NIK",
        "highest level in more than five years",
        "last three trading sessions",
        "climbed a further 1.2% Wednesday",
        "1.2% Wednesday",
        "five years",
        "three trading sessions",
        "trading sessions",
        "Wednesday",
    ]
    
    for phrase in phrases:
        combinations = [
            phrase,
            "Nintondo" + phrase,
            phrase + "Nintondo",
            phrase.replace(" ", ""),
            phrase.replace("'", ""),
            phrase.lower(),
            phrase.upper(),
        ]
        
        for combo in set(combinations):  # Remove duplicates
            if len(combo) >= 4:  # Only test meaningful combinations
                print(f"Testing phrase: '{combo[:50]}{'...' if len(combo) > 50 else ''}'")
                
                hash_result = sha256_hash(combo)
                if test_private_key(hash_result):
                    print(f"🎯 TREASURE FOUND! Phrase '{combo}': {hash_result}")
                    return hash_result
    
    return None

def main():
    """Main execution function"""
    print("🏴‍☠️ MAY 22, 2013 TREASURE HUNT")
    print("=" * 60)
    print("Testing the specific date mentioned in the decoded comment...")
    print(f"Target: {TARGET_PUBKEY[:32]}...")
    
    # Test different approaches
    approaches = [
        test_may_22_2013_variations,
        test_nikkei_may_22_combinations,
        test_time_combinations,
        test_percentage_combinations,
        test_full_sentence_variations,
    ]
    
    for approach_func in approaches:
        result = approach_func()
        if result:
            print(f"\n🏆 TREASURE DISCOVERED: {result}")
            return result
    
    print(f"\n💔 No treasure found in May 22, 2013 analysis")

if __name__ == "__main__":
    main()
