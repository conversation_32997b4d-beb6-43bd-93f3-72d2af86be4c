#!/usr/bin/env python3
"""
DECODE DOGECOIN COMMENT HEX
===========================

Decode the long hex string in <PERSON><PERSON><PERSON><PERSON>'s main.cpp comment and test it.

Target Genesis Public Key: 040184710fa689ad5023690c80f3a49c8f13f8d45b8c857fbcbc8bc4a8e4d3eb4b10f4d4604fa08dce601aaf0f470216fe1b51850b4acf21b179c45070ac7b03a9
"""

import hashlib
import binascii
from ecdsa import Signing<PERSON><PERSON>, SECP256k1

# Target genesis public key
TARGET_PUBKEY = "040184710fa689ad5023690c80f3a49c8f13f8d45b8c857fbcbc8bc4a8e4d3eb4b10f4d4604fa08dce601aaf0f470216fe1b51850b4acf21b179c45070ac7b03a9"

def sha256_hash(text: str) -> str:
    """Generate SHA256 hash of text"""
    return hashlib.sha256(text.encode()).hexdigest()

def test_private_key(private_key_hex: str) -> bool:
    """Test if a hex string generates the target public key"""
    try:
        if private_key_hex.startswith('0x'):
            private_key_hex = private_key_hex[2:]
        
        if len(private_key_hex) != 64:
            return False
            
        private_key_int = int(private_key_hex, 16)
        
        if private_key_int <= 0 or private_key_int >= 0xFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFEBAAEDCE6AF48A03BBFD25E8CD0364141:
            return False
        
        sk = SigningKey.from_secret_exponent(private_key_int, curve=SECP256k1)
        vk = sk.get_verifying_key()
        
        public_key_bytes = b'\x04' + vk.to_string()
        public_key_hex = public_key_bytes.hex()
        
        return public_key_hex.lower() == TARGET_PUBKEY.lower()
        
    except Exception as e:
        return False

def decode_and_test_dogecoin_comment():
    """Decode the long hex string from Dogecoin comment"""
    print("🐕 DECODING DOGECOIN COMMENT HEX")
    print("=" * 50)
    
    # The long hex string from line 2090 in dogecoin/src/main.cpp
    comment_hex = "04ffff001d01044cd14d61792032322c20323031332c2031323a313620612e6d2e204544543a204a6170616e9273204e696b6b65692053746f636b2041766572616765204a503a4e494b202b312e3737252c20776869636820656e6465642061742074686569722068696768657374206c6576656c20696e206d6f7265207468616e206669766520796561727320696e2065616368206f6620746865206c6173742074687265652074726164696e672073657373696f6e732c20636c696d6265642061206675727468657220312e3225205765646e6573646179"
    
    print(f"Comment hex length: {len(comment_hex)} characters")
    print(f"Comment hex: {comment_hex[:64]}...")
    
    # Decode the hex to see what it says
    try:
        decoded_bytes = binascii.unhexlify(comment_hex)
        decoded_text = decoded_bytes.decode('utf-8', errors='ignore')
        print(f"Decoded text: {decoded_text}")
    except Exception as e:
        print(f"Error decoding: {e}")
        return None
    
    # Test various transformations of this hex string and decoded text
    candidates = [
        ("Full comment hex", comment_hex),
        ("SHA256(comment hex)", sha256_hash(comment_hex)),
        ("SHA256(decoded text)", sha256_hash(decoded_text)),
        ("Nintondo + comment hex", sha256_hash("Nintondo" + comment_hex)),
        ("Nintondo + decoded text", sha256_hash("Nintondo" + decoded_text)),
        ("Comment hex + Nintondo", sha256_hash(comment_hex + "Nintondo")),
        ("Decoded text + Nintondo", sha256_hash(decoded_text + "Nintondo")),
    ]
    
    # Also test parts of the hex string
    # The first part looks like Bitcoin-style data
    first_part = comment_hex[:16]  # "04ffff001d01044c"
    candidates.extend([
        ("First part", first_part.ljust(64, '0')),
        ("SHA256(first part)", sha256_hash(first_part)),
        ("Nintondo + first part", sha256_hash("Nintondo" + first_part)),
    ])
    
    # Test the date part if we can extract it
    if "2013" in decoded_text:
        candidates.extend([
            ("SHA256('2013')", sha256_hash("2013")),
            ("Nintondo2013", sha256_hash("Nintondo2013")),
        ])
    
    # Test each candidate
    for name, candidate in candidates:
        print(f"\nTesting {name}:")
        print(f"  Value: {candidate[:64]}{'...' if len(candidate) > 64 else ''}")
        
        if test_private_key(candidate):
            print(f"🎯 TREASURE FOUND! {name}: {candidate}")
            return candidate
        else:
            print(f"  ❌ No match")
    
    return None

def test_bitcoin_genesis_reference():
    """Test if this references Bitcoin's genesis block"""
    print("\n₿ TESTING BITCOIN GENESIS REFERENCE")
    print("=" * 50)
    
    # Bitcoin's genesis block coinbase message
    bitcoin_genesis_text = "The Times 03/Jan/2009 Chancellor on brink of second bailout for banks"
    
    candidates = [
        ("Bitcoin genesis text", bitcoin_genesis_text),
        ("SHA256(Bitcoin genesis)", sha256_hash(bitcoin_genesis_text)),
        ("Nintondo + Bitcoin genesis", sha256_hash("Nintondo" + bitcoin_genesis_text)),
        ("Bitcoin genesis + Nintondo", sha256_hash(bitcoin_genesis_text + "Nintondo")),
    ]
    
    for name, candidate in candidates:
        print(f"Testing {name}: {candidate[:32]}...")
        if test_private_key(candidate):
            print(f"🎯 TREASURE FOUND! {name}: {candidate}")
            return candidate
    
    return None

def test_nikkei_reference():
    """Test references to Nikkei (Japanese stock market)"""
    print("\n📈 TESTING NIKKEI REFERENCES")
    print("=" * 50)
    
    # From the decoded text, it mentions Nikkei
    nikkei_terms = [
        "Nikkei",
        "Japan",
        "Stock",
        "Average",
        "NIK",
        "Wednesday",
        "EDT",
        "1.77%",
        "1.2%",
    ]
    
    for term in nikkei_terms:
        print(f"Testing Nikkei term: '{term}'")
        
        hash_result = sha256_hash(term)
        if test_private_key(hash_result):
            print(f"🎯 TREASURE FOUND! Nikkei term '{term}': {hash_result}")
            return hash_result
        
        # With Nintondo
        nintondo_combo = sha256_hash("Nintondo" + term)
        if test_private_key(nintondo_combo):
            print(f"🎯 TREASURE FOUND! Nintondo+'{term}': {nintondo_combo}")
            return nintondo_combo
    
    return None

def test_date_time_references():
    """Test date and time references from the decoded text"""
    print("\n🕐 TESTING DATE/TIME REFERENCES")
    print("=" * 50)
    
    # Extract date/time info from "Dec 2, 2013, 12:16 a.m. EDT"
    datetime_candidates = [
        "Dec 2, 2013",
        "12:16 a.m.",
        "Dec 2 2013",
        "1216",
        "Dec2013",
        "December2013",
        "20131202",
        "02122013",
        "121600",
        "1216am",
    ]
    
    for dt in datetime_candidates:
        print(f"Testing datetime: '{dt}'")
        
        hash_result = sha256_hash(dt)
        if test_private_key(hash_result):
            print(f"🎯 TREASURE FOUND! DateTime '{dt}': {hash_result}")
            return hash_result
        
        # With Nintondo
        nintondo_combo = sha256_hash("Nintondo" + dt)
        if test_private_key(nintondo_combo):
            print(f"🎯 TREASURE FOUND! Nintondo+'{dt}': {nintondo_combo}")
            return nintondo_combo
    
    return None

def main():
    """Main execution function"""
    print("🏴‍☠️ DOGECOIN COMMENT TREASURE HUNT")
    print("=" * 60)
    print("Analyzing the long hex string in Dogecoin's genesis comment...")
    print(f"Target: {TARGET_PUBKEY[:32]}...")
    
    # Test different approaches
    approaches = [
        decode_and_test_dogecoin_comment,
        test_bitcoin_genesis_reference,
        test_nikkei_reference,
        test_date_time_references,
    ]
    
    for approach_func in approaches:
        result = approach_func()
        if result:
            print(f"\n🏆 TREASURE DISCOVERED: {result}")
            return result
    
    print(f"\n💔 No treasure found in Dogecoin comment analysis")

if __name__ == "__main__":
    main()
