#!/usr/bin/env python3
"""
VERIFY DOGECOIN ADDRESS GENERATION
==================================

Verify that our target public key generates the correct Dogecoin address
that receives the 88 DOGE in the genesis block.

Target Genesis Public Key: 040184710fa689ad5023690c80f3a49c8f13f8d45b8c857fbcbc8bc4a8e4d3eb4b10f4d4604fa08dce601aaf0f470216fe1b51850b4acf21b179c45070ac7b03a9
"""

import hashlib
import base58

# Target genesis public key
TARGET_PUBKEY = "040184710fa689ad5023690c80f3a49c8f13f8d45b8c857fbcbc8bc4a8e4d3eb4b10f4d4604fa08dce601aaf0f470216fe1b51850b4acf21b179c45070ac7b03a9"

def sha256_hash(data):
    """SHA256 hash"""
    return hashlib.sha256(data).digest()

def ripemd160_hash(data):
    """RIPEMD160 hash"""
    return hashlib.new('ripemd160', data).digest()

def hash160(data):
    """Bitcoin-style hash160 (SHA256 then RIPEMD160)"""
    return ripemd160_hash(sha256_hash(data))

def pubkey_to_address_step_by_step(pubkey_hex, version_byte):
    """Convert public key to address with detailed steps"""
    print(f"🔍 STEP-BY-STEP ADDRESS GENERATION")
    print(f"Input public key: {pubkey_hex}")
    print(f"Version byte: 0x{version_byte:02x} ({version_byte})")
    print()
    
    # Step 1: Convert hex to bytes
    pubkey_bytes = bytes.fromhex(pubkey_hex)
    print(f"Step 1 - Public key bytes ({len(pubkey_bytes)} bytes):")
    print(f"  {pubkey_bytes.hex()}")
    print()
    
    # Step 2: SHA256
    sha256_result = sha256_hash(pubkey_bytes)
    print(f"Step 2 - SHA256 of public key:")
    print(f"  {sha256_result.hex()}")
    print()
    
    # Step 3: RIPEMD160
    ripemd160_result = ripemd160_hash(sha256_result)
    print(f"Step 3 - RIPEMD160 of SHA256 (Hash160):")
    print(f"  {ripemd160_result.hex()}")
    print()
    
    # Step 4: Add version byte
    versioned_hash = bytes([version_byte]) + ripemd160_result
    print(f"Step 4 - Add version byte:")
    print(f"  {versioned_hash.hex()}")
    print()
    
    # Step 5: Double SHA256 for checksum
    checksum_full = sha256_hash(sha256_hash(versioned_hash))
    checksum = checksum_full[:4]
    print(f"Step 5 - Double SHA256 for checksum:")
    print(f"  Full: {checksum_full.hex()}")
    print(f"  First 4 bytes: {checksum.hex()}")
    print()
    
    # Step 6: Combine versioned hash + checksum
    full_address_bytes = versioned_hash + checksum
    print(f"Step 6 - Combine versioned hash + checksum:")
    print(f"  {full_address_bytes.hex()}")
    print()
    
    # Step 7: Base58 encode
    address = base58.b58encode(full_address_bytes).decode()
    print(f"Step 7 - Base58 encode:")
    print(f"  {address}")
    print()
    
    return address

def verify_dogecoin_address():
    """Verify the Dogecoin address generation"""
    print("🐕 DOGECOIN ADDRESS VERIFICATION")
    print("=" * 60)
    
    # Dogecoin version byte is 0x1e (30)
    dogecoin_version_byte = 0x1e
    
    # Generate the address step by step
    generated_address = pubkey_to_address_step_by_step(TARGET_PUBKEY, dogecoin_version_byte)
    
    print("🎯 RESULT:")
    print(f"Generated Dogecoin address: {generated_address}")
    
    # Check if this matches what we expect
    expected_address = "DQmCZQo3thCvTxkyAhPHfY7DVLqFtJ2ji6"
    print(f"Expected address:           {expected_address}")
    
    if generated_address == expected_address:
        print("✅ PERFECT MATCH! The public key generates the correct Dogecoin address!")
        return True
    else:
        print("❌ NO MATCH! Something is wrong with our calculation.")
        return False

def verify_bellscoin_address():
    """Verify the Bellscoin address generation"""
    print("\n🔔 BELLSCOIN ADDRESS VERIFICATION")
    print("=" * 60)
    
    # Bellscoin version byte is 0x19 (25)
    bellscoin_version_byte = 0x19
    
    # Generate the address step by step
    generated_address = pubkey_to_address_step_by_step(TARGET_PUBKEY, bellscoin_version_byte)
    
    print("🎯 RESULT:")
    print(f"Generated Bellscoin address: {generated_address}")
    
    return generated_address

def test_other_version_bytes():
    """Test other common version bytes to see what addresses they produce"""
    print("\n🔄 TESTING OTHER VERSION BYTES")
    print("=" * 60)
    
    version_bytes = [
        (0x00, "Bitcoin"),
        (0x05, "Bitcoin Script"),
        (0x1e, "Dogecoin"),
        (0x16, "Dogecoin Script"),
        (0x30, "Litecoin"),
        (0x32, "Litecoin Script"),
        (0x19, "Bellscoin"),
        (0x1a, "Bellscoin Script"),
    ]
    
    for version_byte, name in version_bytes:
        # Quick address generation without detailed steps
        pubkey_bytes = bytes.fromhex(TARGET_PUBKEY)
        hash160_result = hash160(pubkey_bytes)
        versioned_hash = bytes([version_byte]) + hash160_result
        checksum = sha256_hash(sha256_hash(versioned_hash))[:4]
        full_address_bytes = versioned_hash + checksum
        address = base58.b58encode(full_address_bytes).decode()
        
        print(f"{name:20} (0x{version_byte:02x}): {address}")

def main():
    """Main execution function"""
    print("🏴‍☠️ ADDRESS VERIFICATION")
    print("=" * 60)
    print("Verifying that our public key generates the correct addresses...")
    print()
    
    # Verify Dogecoin address
    dogecoin_match = verify_dogecoin_address()
    
    # Verify Bellscoin address
    bellscoin_address = verify_bellscoin_address()
    
    # Test other version bytes
    test_other_version_bytes()
    
    print("\n📋 SUMMARY:")
    print(f"Target public key: {TARGET_PUBKEY}")
    print(f"Dogecoin address:  DQmCZQo3thCvTxkyAhPHfY7DVLqFtJ2ji6 {'✅' if dogecoin_match else '❌'}")
    print(f"Bellscoin address: {bellscoin_address}")
    
    if dogecoin_match:
        print("\n✅ CONFIRMED: Our public key correctly generates the Dogecoin genesis address!")
        print("This means we are definitely looking for the right private key.")
    else:
        print("\n❌ ERROR: Something is wrong with our address generation.")

if __name__ == "__main__":
    main()
