#!/usr/bin/env python3
"""
ADVANCED PATTERN TREASURE HUNT
==============================

This script looks for more subtle patterns and transformations:
1. Numeric constants converted to hex
2. ASCII strings converted to hex
3. Bit manipulations (XOR, shifts, etc.)
4. Mathematical operations on known values
5. Hidden patterns in comments and strings

Target Genesis Public Key: 040184710fa689ad5023690c80f3a49c8f13f8d45b8c857fbcbc8bc4a8e4d3eb4b10f4d4604fa08dce601aaf0f470216fe1b51850b4acf21b179c45070ac7b03a9
"""

import os
import re
import hashlib
import binascii
from typing import List, Dict, Set, Tuple
from ecdsa import SigningKey, SECP256k1

# Target genesis public key
TARGET_PUBKEY = "040184710fa689ad5023690c80f3a49c8f13f8d45b8c857fbcbc8bc4a8e4d3eb4b10f4d4604fa08dce601aaf0f470216fe1b51850b4acf21b179c45070ac7b03a9"

def sha256_hash(text: str) -> str:
    """Generate SHA256 hash of text"""
    return hashlib.sha256(text.encode()).hexdigest()

def test_private_key(private_key_hex: str) -> bool:
    """Test if a hex string generates the target public key"""
    try:
        # Remove 0x prefix if present
        if private_key_hex.startswith('0x'):
            private_key_hex = private_key_hex[2:]
        
        # Ensure it's 64 characters (32 bytes)
        if len(private_key_hex) != 64:
            return False
            
        # Convert to integer
        private_key_int = int(private_key_hex, 16)
        
        # Check if it's in valid range for secp256k1
        if private_key_int <= 0 or private_key_int >= 0xFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFEBAAEDCE6AF48A03BBFD25E8CD0364141:
            return False
        
        # Generate public key
        sk = SigningKey.from_secret_exponent(private_key_int, curve=SECP256k1)
        vk = sk.get_verifying_key()
        
        # Get uncompressed public key (65 bytes: 04 + 32 + 32)
        public_key_bytes = b'\x04' + vk.to_string()
        public_key_hex = public_key_bytes.hex()
        
        return public_key_hex.lower() == TARGET_PUBKEY.lower()
        
    except Exception as e:
        return False

def extract_numeric_constants(content: str) -> Set[str]:
    """Extract numeric constants that could be converted to hex"""
    constants = set()
    
    # Large decimal numbers (6+ digits)
    decimal_pattern = r'\b(\d{6,})\b'
    matches = re.findall(decimal_pattern, content)
    constants.update(matches)
    
    # Hex numbers with 0x prefix
    hex_pattern = r'0x([0-9a-fA-F]+)'
    matches = re.findall(hex_pattern, content)
    constants.update(matches)
    
    # Numbers in #define statements
    define_pattern = r'#define\s+\w+\s+(\d+)'
    matches = re.findall(define_pattern, content)
    constants.update(matches)
    
    return constants

def extract_ascii_strings(content: str) -> Set[str]:
    """Extract ASCII strings that could be converted to hex"""
    strings = set()
    
    # Quoted strings
    string_pattern = r'["\']([A-Za-z0-9_\-\.]+)["\']'
    matches = re.findall(string_pattern, content)
    strings.update(matches)
    
    # Comment strings
    comment_pattern = r'//\s*([A-Za-z0-9_\-\.]+)'
    matches = re.findall(comment_pattern, content)
    strings.update(matches)
    
    return strings

def test_numeric_transformations():
    """Test transformations of known numeric constants"""
    print("🔢 TESTING NUMERIC TRANSFORMATIONS")
    print("=" * 50)
    
    # Known important numbers
    numbers = [
        ("Bellscoin timestamp", 1383509530),
        ("Dogecoin timestamp", 1386325540),
        ("Bellscoin nNonce", 44481),
        ("Dogecoin nNonce", 99943),
        ("Script constant", 486604799),
        ("nBits", 0x1e0ffff0),
        ("Genesis time diff", 1386325540 - 1383509530),  # 2816010
    ]
    
    for name, number in numbers:
        print(f"\nTesting {name}: {number}")
        
        # Convert to hex and test
        hex_value = hex(number)[2:].zfill(64)
        print(f"  As hex: {hex_value}")
        if test_private_key(hex_value):
            print(f"🎯 TREASURE FOUND! {name} as hex: {hex_value}")
            return hex_value
        
        # Hash the number
        hash_result = sha256_hash(str(number))
        print(f"  SHA256: {hash_result}")
        if test_private_key(hash_result):
            print(f"🎯 TREASURE FOUND! SHA256({name}): {hash_result}")
            return hash_result
        
        # With Nintondo
        nintondo_combo = sha256_hash("Nintondo" + str(number))
        print(f"  Nintondo+: {nintondo_combo[:32]}...")
        if test_private_key(nintondo_combo):
            print(f"🎯 TREASURE FOUND! Nintondo+{name}: {nintondo_combo}")
            return nintondo_combo
    
    return None

def test_ascii_transformations():
    """Test ASCII string transformations"""
    print("\n📝 TESTING ASCII TRANSFORMATIONS")
    print("=" * 50)
    
    # Important strings
    strings = [
        "Nintondo",
        "Nintendo", 
        "Dogecoin",
        "Bellscoin",
        "treasure",
        "private",
        "key",
        "secret",
        "hidden",
        "2013",
        "launchpad",
        "forgotten",
    ]
    
    for string in strings:
        print(f"\nTesting string: '{string}'")
        
        # ASCII to hex
        ascii_hex = string.encode().hex().ljust(64, '0')
        print(f"  ASCII hex: {ascii_hex}")
        if test_private_key(ascii_hex):
            print(f"🎯 TREASURE FOUND! ASCII hex of '{string}': {ascii_hex}")
            return ascii_hex
        
        # SHA256 of string
        hash_result = sha256_hash(string)
        print(f"  SHA256: {hash_result}")
        if test_private_key(hash_result):
            print(f"🎯 TREASURE FOUND! SHA256('{string}'): {hash_result}")
            return hash_result
    
    return None

def test_bit_manipulations():
    """Test bit manipulations on the checkpoint hash"""
    print("\n🔀 TESTING BIT MANIPULATIONS")
    print("=" * 50)
    
    checkpoint = "e5be24df57c43a82d15c2f06bda961296948f8f8eb48501bed1efb929afe0698"
    checkpoint_int = int(checkpoint, 16)
    
    manipulations = [
        ("Bitwise NOT", ~checkpoint_int & ((1 << 256) - 1)),
        ("Left shift 1", (checkpoint_int << 1) & ((1 << 256) - 1)),
        ("Right shift 1", checkpoint_int >> 1),
        ("XOR with 0xFF...FF", checkpoint_int ^ ((1 << 256) - 1)),
        ("XOR with 0x55...55", checkpoint_int ^ int("55" * 32, 16)),
        ("XOR with 0xAA...AA", checkpoint_int ^ int("AA" * 32, 16)),
        ("Reverse bytes", int.from_bytes(bytes.fromhex(checkpoint)[::-1], 'big')),
    ]
    
    for name, result in manipulations:
        result_hex = hex(result)[2:].zfill(64)
        print(f"  {name}: {result_hex}")
        if test_private_key(result_hex):
            print(f"🎯 TREASURE FOUND! {name}: {result_hex}")
            return result_hex
    
    return None

def test_mathematical_operations():
    """Test mathematical operations on known values"""
    print("\n🧮 TESTING MATHEMATICAL OPERATIONS")
    print("=" * 50)
    
    checkpoint = int("e5be24df57c43a82d15c2f06bda961296948f8f8eb48501bed1efb929afe0698", 16)
    bell_time = 1383509530
    doge_time = 1386325540
    bell_nonce = 44481
    doge_nonce = 99943
    
    operations = [
        ("checkpoint + bell_time", checkpoint + bell_time),
        ("checkpoint - bell_time", checkpoint - bell_time),
        ("checkpoint + doge_time", checkpoint + doge_time),
        ("checkpoint - doge_time", checkpoint - doge_time),
        ("checkpoint XOR bell_time", checkpoint ^ bell_time),
        ("checkpoint XOR doge_time", checkpoint ^ doge_time),
        ("checkpoint + (doge_time - bell_time)", checkpoint + (doge_time - bell_time)),
        ("checkpoint * bell_nonce", (checkpoint * bell_nonce) & ((1 << 256) - 1)),
        ("checkpoint * doge_nonce", (checkpoint * doge_nonce) & ((1 << 256) - 1)),
    ]
    
    for name, result in operations:
        result_hex = hex(result)[2:].zfill(64)
        print(f"  {name}: {result_hex[:32]}...")
        if test_private_key(result_hex):
            print(f"🎯 TREASURE FOUND! {name}: {result_hex}")
            return result_hex
    
    return None

def test_steganographic_patterns():
    """Look for steganographic patterns in the codebase"""
    print("\n🕵️ TESTING STEGANOGRAPHIC PATTERNS")
    print("=" * 50)
    
    # Look for patterns in comments, variable names, etc.
    patterns = []
    
    # Scan for suspicious patterns
    for root, dirs, files in os.walk('.'):
        if any(skip in root for skip in ['.git', '__pycache__']):
            continue
            
        for file in files:
            if file.endswith(('.cpp', '.h', '.py')):
                filepath = os.path.join(root, file)
                try:
                    with open(filepath, 'r', encoding='utf-8', errors='ignore') as f:
                        content = f.read()
                        
                        # Look for repeated patterns
                        repeated_pattern = r'([0-9a-fA-F])\1{3,}'
                        matches = re.findall(repeated_pattern, content)
                        if matches:
                            patterns.extend(matches)
                            
                        # Look for alternating patterns
                        alternating_pattern = r'([0-9a-fA-F])([0-9a-fA-F])\1\2{2,}'
                        matches = re.findall(alternating_pattern, content)
                        if matches:
                            patterns.extend([''.join(match) for match in matches])
                            
                except Exception:
                    continue
    
    # Test discovered patterns
    for pattern in set(patterns):
        if len(pattern) >= 4:
            # Extend pattern to 64 chars
            extended = (pattern * (64 // len(pattern) + 1))[:64]
            print(f"  Pattern '{pattern}' extended: {extended}")
            if test_private_key(extended):
                print(f"🎯 TREASURE FOUND! Pattern '{pattern}': {extended}")
                return extended
    
    return None

def main():
    """Main execution function"""
    print("🏴‍☠️ ADVANCED PATTERN TREASURE HUNT")
    print("=" * 60)
    print("Searching for subtle patterns and transformations...")
    print(f"Target: {TARGET_PUBKEY[:32]}...")
    
    # Test different transformation types
    tests = [
        test_numeric_transformations,
        test_ascii_transformations,
        test_bit_manipulations,
        test_mathematical_operations,
        test_steganographic_patterns,
    ]
    
    for test_func in tests:
        result = test_func()
        if result:
            print(f"\n🏆 TREASURE DISCOVERED: {result}")
            return result
    
    print(f"\n💔 No treasure found in advanced pattern analysis")
    print("🔍 The key might be hidden in an even more subtle way...")

if __name__ == "__main__":
    main()
