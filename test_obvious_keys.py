#!/usr/bin/env python3
"""
TEST OBVIOUS PRIVATE KEYS
=========================

Sometimes the answer is hiding in plain sight.
Let's test very obvious, simple private keys that might have been overlooked.

Target Genesis Public Key: 040184710fa689ad5023690c80f3a49c8f13f8d45b8c857fbcbc8bc4a8e4d3eb4b10f4d4604fa08dce601aaf0f470216fe1b51850b4acf21b179c45070ac7b03a9
"""

import hashlib
from ecdsa import SigningKey, SECP256k1

# Target genesis public key
TARGET_PUBKEY = "040184710fa689ad5023690c80f3a49c8f13f8d45b8c857fbcbc8bc4a8e4d3eb4b10f4d4604fa08dce601aaf0f470216fe1b51850b4acf21b179c45070ac7b03a9"

def test_private_key(private_key_hex: str) -> bool:
    """Test if a hex string generates the target public key"""
    try:
        if private_key_hex.startswith('0x'):
            private_key_hex = private_key_hex[2:]
        
        if len(private_key_hex) != 64:
            return False
            
        private_key_int = int(private_key_hex, 16)
        
        if private_key_int <= 0 or private_key_int >= 0xFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFEBAAEDCE6AF48A03BBFD25E8CD0364141:
            return False
        
        sk = SigningKey.from_secret_exponent(private_key_int, curve=SECP256k1)
        vk = sk.get_verifying_key()
        
        public_key_bytes = b'\x04' + vk.to_string()
        public_key_hex = public_key_bytes.hex()
        
        return public_key_hex.lower() == TARGET_PUBKEY.lower()
        
    except Exception as e:
        return False

def test_sequential_keys():
    """Test very simple sequential private keys"""
    print("🔢 TESTING SEQUENTIAL KEYS")
    print("=" * 40)
    
    # Test simple sequential numbers
    for i in range(1, 1000):
        private_key = hex(i)[2:].zfill(64)
        print(f"Testing key {i}: {private_key[:16]}...")
        
        if test_private_key(private_key):
            print(f"🎯 TREASURE FOUND! Sequential key {i}: {private_key}")
            return private_key
    
    return None

def test_obvious_hex_patterns():
    """Test obvious hex patterns"""
    print("\n🎯 TESTING OBVIOUS HEX PATTERNS")
    print("=" * 40)
    
    patterns = [
        # All same digit
        "1" * 64,
        "2" * 64,
        "3" * 64,
        "4" * 64,
        "5" * 64,
        "6" * 64,
        "7" * 64,
        "8" * 64,
        "9" * 64,
        "a" * 64,
        "b" * 64,
        "c" * 64,
        "d" * 64,
        "e" * 64,
        "f" * 64,
        
        # Alternating patterns
        ("01" * 32),
        ("10" * 32),
        ("12" * 32),
        ("21" * 32),
        ("ab" * 32),
        ("ba" * 32),
        ("ff" * 32),
        ("00" * 32),
        
        # Incrementing
        "".join(f"{i%16:x}" for i in range(64)),
        "".join(f"{i%10}" for i in range(64)),
        
        # Common test patterns
        "deadbeef" * 8,
        "cafebabe" * 8,
        "feedface" * 8,
        "badcafe0" * 8,
        "12345678" * 8,
        "87654321" * 8,
        "abcdef01" * 8,
        "fedcba98" * 8,
    ]
    
    for pattern in patterns:
        print(f"Testing pattern: {pattern[:32]}...")
        if test_private_key(pattern):
            print(f"🎯 TREASURE FOUND! Pattern: {pattern}")
            return pattern
    
    return None

def test_nintendo_variations():
    """Test Nintendo/Nintondo variations as direct hex"""
    print("\n🎮 TESTING NINTENDO VARIATIONS AS HEX")
    print("=" * 40)
    
    # Convert Nintendo-related strings to hex and pad
    nintendo_strings = [
        "Nintendo",
        "Nintondo", 
        "nintendo",
        "nintondo",
        "NINTENDO",
        "NINTONDO",
        "NintendoNintendo",
        "NintondoNintondo",
    ]
    
    for string in nintendo_strings:
        # Convert to hex
        hex_string = string.encode().hex()
        # Pad to 64 characters
        padded_hex = hex_string.ljust(64, '0')
        
        print(f"Testing '{string}' as hex: {padded_hex[:32]}...")
        if test_private_key(padded_hex):
            print(f"🎯 TREASURE FOUND! '{string}' as hex: {padded_hex}")
            return padded_hex
        
        # Also try right-padding with different characters
        for pad_char in ['0', 'f', '1']:
            padded_alt = hex_string.ljust(64, pad_char)
            if test_private_key(padded_alt):
                print(f"🎯 TREASURE FOUND! '{string}' padded with '{pad_char}': {padded_alt}")
                return padded_alt
    
    return None

def test_timestamp_as_hex():
    """Test timestamps as direct hex values"""
    print("\n⏰ TESTING TIMESTAMPS AS HEX")
    print("=" * 40)
    
    timestamps = [
        1383509530,  # Bellscoin timestamp
        1386325540,  # Dogecoin timestamp
        1369180800,  # May 22, 2013 approximate
        1361718171,  # Bellscoin testnet timestamp
        486604799,   # Script constant
    ]
    
    for ts in timestamps:
        # Convert timestamp to hex and pad
        hex_ts = hex(ts)[2:].zfill(64)
        print(f"Testing timestamp {ts} as hex: {hex_ts[:32]}...")
        
        if test_private_key(hex_ts):
            print(f"🎯 TREASURE FOUND! Timestamp {ts}: {hex_ts}")
            return hex_ts
        
        # Also try with different padding
        hex_ts_left = hex(ts)[2:].rjust(64, '0')
        if test_private_key(hex_ts_left):
            print(f"🎯 TREASURE FOUND! Timestamp {ts} (left-padded): {hex_ts_left}")
            return hex_ts_left
    
    return None

def test_nonce_as_hex():
    """Test nonces as direct hex values"""
    print("\n🎲 TESTING NONCES AS HEX")
    print("=" * 40)
    
    nonces = [
        44481,   # Bellscoin nonce
        99943,   # Dogecoin nonce
        0,       # Testnet nonce
        1,       # Simple nonce
        12345,   # Common test nonce
        54321,   # Reverse
    ]
    
    for nonce in nonces:
        # Convert nonce to hex and pad
        hex_nonce = hex(nonce)[2:].zfill(64)
        print(f"Testing nonce {nonce} as hex: {hex_nonce[:32]}...")
        
        if test_private_key(hex_nonce):
            print(f"🎯 TREASURE FOUND! Nonce {nonce}: {hex_nonce}")
            return hex_nonce
    
    return None

def test_public_key_manipulation():
    """Test manipulations of the public key itself"""
    print("\n🔑 TESTING PUBLIC KEY MANIPULATIONS")
    print("=" * 40)
    
    pubkey = TARGET_PUBKEY.lower()
    
    # Extract coordinates
    x_coord = pubkey[2:66]  # Skip '04' prefix
    y_coord = pubkey[66:]
    
    manipulations = [
        # Try the coordinates directly
        ("X coordinate", x_coord),
        ("Y coordinate", y_coord),
        
        # Simple arithmetic on coordinates
        ("X + 1", hex(int(x_coord, 16) + 1)[2:].zfill(64)),
        ("X - 1", hex(int(x_coord, 16) - 1)[2:].zfill(64)),
        ("Y + 1", hex(int(y_coord, 16) + 1)[2:].zfill(64)),
        ("Y - 1", hex(int(y_coord, 16) - 1)[2:].zfill(64)),
        
        # Bit operations
        ("NOT X", hex((~int(x_coord, 16)) & ((1 << 256) - 1))[2:].zfill(64)),
        ("NOT Y", hex((~int(y_coord, 16)) & ((1 << 256) - 1))[2:].zfill(64)),
    ]
    
    for name, candidate in manipulations:
        print(f"Testing {name}: {candidate[:32]}...")
        if test_private_key(candidate):
            print(f"🎯 TREASURE FOUND! {name}: {candidate}")
            return candidate
    
    return None

def test_brute_force_small_range():
    """Brute force a small range around interesting numbers"""
    print("\n💪 TESTING SMALL BRUTE FORCE RANGES")
    print("=" * 40)
    
    # Test around the script constant
    script_const = 486604799
    print(f"Testing range around script constant {script_const}...")
    
    for offset in range(-100, 101):
        test_val = script_const + offset
        if test_val > 0:
            hex_val = hex(test_val)[2:].zfill(64)
            if test_private_key(hex_val):
                print(f"🎯 TREASURE FOUND! Script constant + {offset}: {hex_val}")
                return hex_val
    
    # Test around nonces
    for base_nonce in [44481, 99943]:
        print(f"Testing range around nonce {base_nonce}...")
        for offset in range(-50, 51):
            test_val = base_nonce + offset
            if test_val > 0:
                hex_val = hex(test_val)[2:].zfill(64)
                if test_private_key(hex_val):
                    print(f"🎯 TREASURE FOUND! Nonce {base_nonce} + {offset}: {hex_val}")
                    return hex_val
    
    return None

def main():
    """Main execution function"""
    print("🏴‍☠️ TESTING OBVIOUS PRIVATE KEYS")
    print("=" * 60)
    print("Sometimes the answer is hiding in plain sight...")
    print(f"Target: {TARGET_PUBKEY[:32]}...")
    
    # Test different obvious approaches
    tests = [
        test_sequential_keys,
        test_obvious_hex_patterns,
        test_nintendo_variations,
        test_timestamp_as_hex,
        test_nonce_as_hex,
        test_public_key_manipulation,
        test_brute_force_small_range,
    ]
    
    for test_func in tests:
        result = test_func()
        if result:
            print(f"\n🏆 TREASURE DISCOVERED: {result}")
            return result
    
    print(f"\n💔 No treasure found in obvious keys")
    print("🤔 The private key might be more sophisticated than expected...")

if __name__ == "__main__":
    main()
