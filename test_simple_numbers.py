#!/usr/bin/env python3
"""
TEST SIMPLE NUMBERS AS PRIVATE KEYS
===================================

Sometimes the answer is incredibly simple.
Let's test the first few thousand integers as private keys.

Target Genesis Public Key: 040184710fa689ad5023690c80f3a49c8f13f8d45b8c857fbcbc8bc4a8e4d3eb4b10f4d4604fa08dce601aaf0f470216fe1b51850b4acf21b179c45070ac7b03a9
"""

from ecdsa import Signing<PERSON><PERSON>, SECP256k1

# Target genesis public key
TARGET_PUBKEY = "040184710fa689ad5023690c80f3a49c8f13f8d45b8c857fbcbc8bc4a8e4d3eb4b10f4d4604fa08dce601aaf0f470216fe1b51850b4acf21b179c45070ac7b03a9"

def test_private_key_int(private_key_int):
    """Test if an integer generates the target public key"""
    try:
        if private_key_int <= 0 or private_key_int >= 0xFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFEBAAEDCE6AF48A03BBFD25E8CD0364141:
            return False
        
        sk = SigningKey.from_secret_exponent(private_key_int, curve=SECP256k1)
        vk = sk.get_verifying_key()
        
        public_key_bytes = b'\x04' + vk.to_string()
        public_key_hex = public_key_bytes.hex()
        
        return public_key_hex.lower() == TARGET_PUBKEY.lower()
        
    except Exception as e:
        return False

def test_sequential_numbers():
    """Test sequential numbers starting from 1"""
    print("🔢 TESTING SEQUENTIAL NUMBERS")
    print("=" * 50)
    
    # Test first 100,000 numbers
    for i in range(1, 100001):
        if i % 10000 == 0:
            print(f"  Testing number {i}...")
        
        if test_private_key_int(i):
            private_key_hex = hex(i)[2:].zfill(64)
            print(f"🎯 TREASURE FOUND! Private key is simply the number {i}")
            print(f"🔑 Private key (hex): {private_key_hex}")
            return private_key_hex
    
    return None

def test_special_numbers():
    """Test special/meaningful numbers"""
    print("\n✨ TESTING SPECIAL NUMBERS")
    print("=" * 50)
    
    special_numbers = [
        # Powers of 2
        (2**8, "2^8 = 256"),
        (2**16, "2^16 = 65536"),
        (2**20, "2^20 = 1048576"),
        (2**24, "2^24"),
        (2**32, "2^32"),
        
        # Factorials
        (1, "1! = 1"),
        (2, "2! = 2"),
        (6, "3! = 6"),
        (24, "4! = 24"),
        (120, "5! = 120"),
        (720, "6! = 720"),
        (5040, "7! = 5040"),
        (40320, "8! = 40320"),
        (362880, "9! = 362880"),
        (3628800, "10! = 3628800"),
        
        # Fibonacci numbers
        (1, "Fib(1) = 1"),
        (1, "Fib(2) = 1"),
        (2, "Fib(3) = 2"),
        (3, "Fib(4) = 3"),
        (5, "Fib(5) = 5"),
        (8, "Fib(6) = 8"),
        (13, "Fib(7) = 13"),
        (21, "Fib(8) = 21"),
        (34, "Fib(9) = 34"),
        (55, "Fib(10) = 55"),
        (89, "Fib(11) = 89"),
        (144, "Fib(12) = 144"),
        (233, "Fib(13) = 233"),
        (377, "Fib(14) = 377"),
        (610, "Fib(15) = 610"),
        (987, "Fib(16) = 987"),
        (1597, "Fib(17) = 1597"),
        (2584, "Fib(18) = 2584"),
        (4181, "Fib(19) = 4181"),
        (6765, "Fib(20) = 6765"),
        
        # Prime numbers
        (2, "Prime 2"),
        (3, "Prime 3"),
        (5, "Prime 5"),
        (7, "Prime 7"),
        (11, "Prime 11"),
        (13, "Prime 13"),
        (17, "Prime 17"),
        (19, "Prime 19"),
        (23, "Prime 23"),
        (29, "Prime 29"),
        (31, "Prime 31"),
        (37, "Prime 37"),
        (41, "Prime 41"),
        (43, "Prime 43"),
        (47, "Prime 47"),
        (53, "Prime 53"),
        (59, "Prime 59"),
        (61, "Prime 61"),
        (67, "Prime 67"),
        (71, "Prime 71"),
        (73, "Prime 73"),
        (79, "Prime 79"),
        (83, "Prime 83"),
        (89, "Prime 89"),
        (97, "Prime 97"),
        
        # Date-related numbers
        (20130522, "May 22, 2013 (YYYYMMDD)"),
        (522013, "May 22, 2013 (MMDDYYYY)"),
        (22052013, "May 22, 2013 (DDMMYYYY)"),
        (2013, "Year 2013"),
        (522, "May 22"),
        (1216, "12:16 (time from comment)"),
        
        # Crypto-related numbers
        (21000000, "Bitcoin max supply"),
        (100000000, "Satoshis in 1 BTC"),
        (88, "Genesis block reward"),
        (486604799, "Script constant"),
        (44481, "Bellscoin nonce"),
        (99943, "Dogecoin nonce"),
        (1383509530, "Bellscoin timestamp"),
        (1386325540, "Dogecoin timestamp"),
        
        # Nintendo-related
        (1985, "Nintendo founded"),
        (1889, "Nintendo original founding"),
        (64, "Nintendo 64"),
        (128, "Nintendo 128-bit era"),
        
        # Common test numbers
        (12345, "12345"),
        (54321, "54321"),
        (123456, "123456"),
        (654321, "654321"),
        (1234567, "1234567"),
        (7654321, "7654321"),
        (12345678, "12345678"),
        (87654321, "87654321"),
        (123456789, "123456789"),
        (987654321, "987654321"),
        
        # Hex-looking numbers
        (0xDEADBEEF, "0xDEADBEEF"),
        (0xCAFEBABE, "0xCAFEBABE"),
        (0xFEEDFACE, "0xFEEDFACE"),
        (0xBADCAFE, "0xBADCAFE"),
        (0x12345678, "0x12345678"),
        (0x87654321, "0x87654321"),
        (0xABCDEF01, "0xABCDEF01"),
        (0xFEDCBA98, "0xFEDCBA98"),
    ]
    
    tested_numbers = set()
    
    for number, description in special_numbers:
        if number in tested_numbers:
            continue
        tested_numbers.add(number)
        
        print(f"Testing {description}: {number}")
        
        if test_private_key_int(number):
            private_key_hex = hex(number)[2:].zfill(64)
            print(f"🎯 TREASURE FOUND! Private key is {description}")
            print(f"🔑 Private key (decimal): {number}")
            print(f"🔑 Private key (hex): {private_key_hex}")
            return private_key_hex
    
    return None

def test_ascii_values():
    """Test ASCII values of important strings"""
    print("\n📝 TESTING ASCII VALUES")
    print("=" * 50)
    
    strings = [
        "Nintondo",
        "Nintendo", 
        "Bellscoin",
        "Dogecoin",
        "treasure",
        "private",
        "key",
        "secret",
        "2013",
        "May 22, 2013",
        "Nikkei",
        "Japan",
    ]
    
    for string in strings:
        # Convert string to integer (treating as big-endian)
        ascii_bytes = string.encode('ascii')
        if len(ascii_bytes) <= 32:  # Only if it fits in 32 bytes
            ascii_int = int.from_bytes(ascii_bytes, 'big')
            
            print(f"Testing ASCII value of '{string}': {ascii_int}")
            
            if test_private_key_int(ascii_int):
                private_key_hex = hex(ascii_int)[2:].zfill(64)
                print(f"🎯 TREASURE FOUND! Private key is ASCII value of '{string}'")
                print(f"🔑 Private key (decimal): {ascii_int}")
                print(f"🔑 Private key (hex): {private_key_hex}")
                return private_key_hex
    
    return None

def main():
    """Main execution function"""
    print("🏴‍☠️ TESTING SIMPLE NUMBERS AS PRIVATE KEYS")
    print("=" * 60)
    print("Sometimes the treasure is hidden in plain sight...")
    print(f"Target: {TARGET_PUBKEY[:32]}...")
    
    # Test different approaches
    approaches = [
        test_sequential_numbers,
        test_special_numbers,
        test_ascii_values,
    ]
    
    for approach_func in approaches:
        result = approach_func()
        if result:
            print(f"\n🏆 TREASURE DISCOVERED: {result}")
            return result
    
    print(f"\n💔 No treasure found in simple numbers")
    print("🤔 The private key might be more complex than expected...")

if __name__ == "__main__":
    main()
