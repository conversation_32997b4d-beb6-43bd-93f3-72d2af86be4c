#!/usr/bin/env python3
"""
Complete Analysis of the Private Key
====================================

Analyzing the private key: e5be24df57c43a82d15c2f06bda961296948f8f8eb48501bed1efb929afe0698

This is a valid private key - let's extract ALL possible information from it:
- The corresponding public key
- Bitcoin/Dogecoin/Bellscoin addresses
- Any patterns or embedded data
- Relationships with known constants

Target Genesis Public Key: 040184710fa689ad5023690c80f3a49c8f13f8d45b8c857fbcbc8bc4a8e4d3eb4b10f4d4604fa08dce601aaf0f470216fe1b51850b4acf21b179c45070ac7b03a9
"""

import hashlib
import binascii
import struct
import base58
from typing import Optional, List, Tuple
from ecdsa import SigningKey, SECP256k1
from ecdsa.util import string_to_number, number_to_string

# The private key to analyze
PRIVATE_KEY = "e5be24df57c43a82d15c2f06bda961296948f8f8eb48501bed1efb929afe0698"

# Target genesis public key for comparison
TARGET_PUBKEY = "040184710fa689ad5023690c80f3a49c8f13f8d45b8c857fbcbc8bc4a8e4d3eb4b10f4d4604fa08dce601aaf0f470216fe1b51850b4acf21b179c45070ac7b03a9"

def analyze_private_key_structure():
    """Analyze the structure and properties of the private key"""
    print("🔑 PRIVATE KEY STRUCTURE ANALYSIS")
    print("=" * 60)
    
    print(f"Private Key: {PRIVATE_KEY}")
    print(f"Length: {len(PRIVATE_KEY)} characters ({len(PRIVATE_KEY)//2} bytes)")
    
    # Convert to integer
    private_key_int = int(PRIVATE_KEY, 16)
    print(f"As Integer: {private_key_int}")
    print(f"In Binary: {bin(private_key_int)}")
    
    # Check if it's in valid range for secp256k1
    secp256k1_order = 0xFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFEBAAEDCE6AF48A03BBFD25E8CD0364141
    print(f"Valid for secp256k1: {0 < private_key_int < secp256k1_order}")
    print(f"Percentage of max: {(private_key_int / secp256k1_order) * 100:.2f}%")
    
    # Analyze byte patterns
    print(f"\nByte Analysis:")
    private_key_bytes = bytes.fromhex(PRIVATE_KEY)
    for i, byte in enumerate(private_key_bytes):
        print(f"  Byte {i:2d}: 0x{byte:02x} = {byte:3d} = '{chr(byte) if 32 <= byte <= 126 else '.'}'")
    
    # Look for embedded timestamps (as we found before)
    print(f"\nEmbedded Timestamps:")
    for i in range(0, len(PRIVATE_KEY) - 8, 2):
        chunk = PRIVATE_KEY[i:i+8]
        try:
            timestamp = int(chunk, 16)
            if 1000000000 <= timestamp <= 2147483647:  # Valid Unix timestamp range
                import datetime
                dt = datetime.datetime.fromtimestamp(timestamp)
                print(f"  Position {i:2d}-{i+7:2d}: {chunk} = {timestamp} = {dt}")
        except:
            pass

def generate_public_key():
    """Generate the public key from the private key"""
    print("\n🔓 PUBLIC KEY GENERATION")
    print("=" * 40)
    
    try:
        # Create signing key
        signing_key = SigningKey.from_string(
            binascii.unhexlify(PRIVATE_KEY), 
            curve=SECP256k1
        )
        
        # Get public key
        verifying_key = signing_key.get_verifying_key()
        public_key_bytes = verifying_key.to_string()
        
        # Uncompressed public key
        uncompressed_pubkey = "04" + public_key_bytes.hex()
        print(f"Uncompressed Public Key: {uncompressed_pubkey}")
        
        # Compressed public key
        x_coord = public_key_bytes[:32]
        y_coord = public_key_bytes[32:]
        y_int = int.from_bytes(y_coord, 'big')
        
        if y_int % 2 == 0:
            compressed_pubkey = "02" + x_coord.hex()
        else:
            compressed_pubkey = "03" + x_coord.hex()
        
        print(f"Compressed Public Key: {compressed_pubkey}")
        
        # Check if it matches the target
        matches_target = uncompressed_pubkey.lower() == TARGET_PUBKEY.lower()
        print(f"Matches Target Genesis Key: {matches_target}")
        
        if matches_target:
            print("🎯 THIS IS THE GENESIS PRIVATE KEY!")
        
        return uncompressed_pubkey, compressed_pubkey
        
    except Exception as e:
        print(f"Error generating public key: {e}")
        return None, None

def generate_addresses(uncompressed_pubkey, compressed_pubkey):
    """Generate various cryptocurrency addresses"""
    print("\n💰 ADDRESS GENERATION")
    print("=" * 30)
    
    if not uncompressed_pubkey:
        return
    
    def hash160(data):
        """RIPEMD160(SHA256(data))"""
        sha256_hash = hashlib.sha256(data).digest()
        ripemd160 = hashlib.new('ripemd160')
        ripemd160.update(sha256_hash)
        return ripemd160.digest()
    
    def base58check_encode(version_byte, payload):
        """Encode with Base58Check"""
        extended = bytes([version_byte]) + payload
        checksum = hashlib.sha256(hashlib.sha256(extended).digest()).digest()[:4]
        return base58.b58encode(extended + checksum).decode()
    
    # Generate addresses for both compressed and uncompressed keys
    for key_type, pubkey in [("Uncompressed", uncompressed_pubkey), ("Compressed", compressed_pubkey)]:
        if not pubkey:
            continue
            
        print(f"\n{key_type} Key Addresses:")
        pubkey_bytes = bytes.fromhex(pubkey)
        hash160_result = hash160(pubkey_bytes)
        
        # Bitcoin addresses
        btc_address = base58check_encode(0x00, hash160_result)  # P2PKH
        print(f"  Bitcoin (P2PKH): {btc_address}")
        
        # Dogecoin addresses (version byte 0x1e = 30)
        doge_address = base58check_encode(0x1e, hash160_result)
        print(f"  Dogecoin: {doge_address}")
        
        # Bellscoin addresses (assuming same as Dogecoin)
        bells_address = base58check_encode(0x1e, hash160_result)
        print(f"  Bellscoin: {bells_address}")
        
        # Testnet addresses
        btc_testnet = base58check_encode(0x6f, hash160_result)
        print(f"  Bitcoin Testnet: {btc_testnet}")

def analyze_mathematical_properties():
    """Analyze mathematical properties of the private key"""
    print("\n🧮 MATHEMATICAL PROPERTIES")
    print("=" * 40)
    
    private_key_int = int(PRIVATE_KEY, 16)
    
    # Basic properties
    print(f"Is Even: {private_key_int % 2 == 0}")
    print(f"Is Prime: {is_prime(private_key_int) if private_key_int < 10**15 else 'Too large to check efficiently'}")
    
    # Bit analysis
    binary = bin(private_key_int)[2:]
    print(f"Number of bits: {len(binary)}")
    print(f"Number of 1s: {binary.count('1')}")
    print(f"Number of 0s: {binary.count('0')}")
    print(f"Hamming weight: {binary.count('1')}")
    
    # Entropy analysis
    byte_counts = {}
    for i in range(0, len(PRIVATE_KEY), 2):
        byte = PRIVATE_KEY[i:i+2]
        byte_counts[byte] = byte_counts.get(byte, 0) + 1
    
    print(f"Unique bytes: {len(byte_counts)}/32")
    print(f"Most common byte: {max(byte_counts.items(), key=lambda x: x[1])}")
    
    # Check for patterns
    print(f"\nPattern Analysis:")
    # Consecutive bytes
    for i in range(len(PRIVATE_KEY) - 4):
        if PRIVATE_KEY[i:i+2] == PRIVATE_KEY[i+2:i+4]:
            print(f"  Repeated byte at position {i}: {PRIVATE_KEY[i:i+2]}")
    
    # Arithmetic sequences
    bytes_list = [int(PRIVATE_KEY[i:i+2], 16) for i in range(0, len(PRIVATE_KEY), 2)]
    for i in range(len(bytes_list) - 2):
        if bytes_list[i+1] - bytes_list[i] == bytes_list[i+2] - bytes_list[i+1]:
            diff = bytes_list[i+1] - bytes_list[i]
            if diff != 0:
                print(f"  Arithmetic sequence at bytes {i}-{i+2}: diff={diff}")

def is_prime(n):
    """Simple primality test for small numbers"""
    if n < 2:
        return False
    if n == 2:
        return True
    if n % 2 == 0:
        return False
    for i in range(3, int(n**0.5) + 1, 2):
        if n % i == 0:
            return False
    return True

def analyze_relationships():
    """Analyze relationships with known constants"""
    print("\n🔗 RELATIONSHIPS WITH KNOWN CONSTANTS")
    print("=" * 50)
    
    private_key_int = int(PRIVATE_KEY, 16)
    
    # Known constants from our previous analysis
    constants = {
        "Bellscoin Genesis Timestamp": 1383509530,
        "Dogecoin Genesis Timestamp": 1386325540,
        "Bellscoin Genesis Nonce": 44481,
        "Dogecoin Genesis Nonce": 99943,
        "Script Constant": 486604799,
        "Discovered Timestamp 1": 1472477826,
        "Discovered Timestamp 2": 1766390008,
    }
    
    for name, value in constants.items():
        # Check if the constant appears in the private key
        value_hex = hex(value)[2:]
        if value_hex in PRIVATE_KEY:
            pos = PRIVATE_KEY.find(value_hex)
            print(f"  {name} found at position {pos}: {value_hex}")
        
        # Check mathematical relationships
        if private_key_int % value == 0:
            print(f"  Private key is divisible by {name}")
        
        # Check XOR relationships
        xor_result = private_key_int ^ value
        if xor_result.bit_length() <= 64:  # Interesting if result is small
            print(f"  Private key XOR {name} = {hex(xor_result)}")

def main():
    """Main analysis function"""
    print("🔍 COMPLETE PRIVATE KEY ANALYSIS")
    print("=" * 70)
    print(f"Analyzing: {PRIVATE_KEY}")
    print("=" * 70)
    
    # Structure analysis
    analyze_private_key_structure()
    
    # Generate public key
    uncompressed, compressed = generate_public_key()
    
    # Generate addresses
    generate_addresses(uncompressed, compressed)
    
    # Mathematical properties
    analyze_mathematical_properties()
    
    # Relationships
    analyze_relationships()
    
    print("\n" + "=" * 70)
    print("ANALYSIS COMPLETE")
    print("=" * 70)

if __name__ == "__main__":
    main()
