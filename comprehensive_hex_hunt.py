#!/usr/bin/env python3
"""
COMPREHENSIVE HEX TREASURE HUNT
===============================

This script searches EVERY hex value in both Bellscoin and Dogecoin codebases.
For each hex value found:
1. Test it directly as a private key
2. Test it without "0x" prefix as a private key  
3. Test SHA256 hash of the hex value
4. Test combinations with "Nintondo"

Target Genesis Public Key: 040184710fa689ad5023690c80f3a49c8f13f8d45b8c857fbcbc8bc4a8e4d3eb4b10f4d4604fa08dce601aaf0f470216fe1b51850b4acf21b179c45070ac7b03a9
"""

import os
import re
import hashlib
from typing import List, Dict, Set, Tuple
from ecdsa import SigningKey, SECP256k1
from ecdsa.util import string_to_number

# Target genesis public key
TARGET_PUBKEY = "040184710fa689ad5023690c80f3a49c8f13f8d45b8c857fbcbc8bc4a8e4d3eb4b10f4d4604fa08dce601aaf0f470216fe1b51850b4acf21b179c45070ac7b03a9"

def sha256_hash(text: str) -> str:
    """Generate SHA256 hash of text"""
    return hashlib.sha256(text.encode()).hexdigest()

def test_private_key(private_key_hex: str) -> bool:
    """Test if a hex string generates the target public key"""
    try:
        # Remove 0x prefix if present
        if private_key_hex.startswith('0x'):
            private_key_hex = private_key_hex[2:]
        
        # Ensure it's 64 characters (32 bytes)
        if len(private_key_hex) != 64:
            return False
            
        # Convert to integer
        private_key_int = int(private_key_hex, 16)
        
        # Check if it's in valid range for secp256k1
        if private_key_int <= 0 or private_key_int >= 0xFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFEBAAEDCE6AF48A03BBFD25E8CD0364141:
            return False
        
        # Generate public key
        sk = SigningKey.from_secret_exponent(private_key_int, curve=SECP256k1)
        vk = sk.get_verifying_key()
        
        # Get uncompressed public key (65 bytes: 04 + 32 + 32)
        public_key_bytes = b'\x04' + vk.to_string()
        public_key_hex = public_key_bytes.hex()
        
        return public_key_hex.lower() == TARGET_PUBKEY.lower()
        
    except Exception as e:
        return False

def extract_all_hex_values(filepath: str) -> Set[str]:
    """Extract ALL possible hex values from a file"""
    hex_values = set()
    
    try:
        with open(filepath, 'r', encoding='utf-8', errors='ignore') as f:
            content = f.read()
            
            # Pattern 1: 0x followed by hex (any length >= 8)
            pattern_0x = r'0x([0-9a-fA-F]{8,})'
            matches = re.findall(pattern_0x, content)
            hex_values.update(matches)
            
            # Pattern 2: Quoted hex strings (any length >= 8)
            pattern_quoted = r'["\']([0-9a-fA-F]{8,})["\']'
            matches = re.findall(pattern_quoted, content)
            hex_values.update(matches)
            
            # Pattern 3: Standalone hex strings (word boundaries, length >= 8)
            pattern_standalone = r'\b([0-9a-fA-F]{8,})\b'
            matches = re.findall(pattern_standalone, content)
            hex_values.update(matches)
            
            # Pattern 4: Hash-like patterns (exactly 32 or 64 chars)
            pattern_hash = r'\b([0-9a-fA-F]{32}|[0-9a-fA-F]{64})\b'
            matches = re.findall(pattern_hash, content)
            hex_values.update(matches)
            
            # Pattern 5: In comments (// or /* */)
            pattern_comment = r'//.*?([0-9a-fA-F]{8,})|/\*.*?([0-9a-fA-F]{8,}).*?\*/'
            matches = re.findall(pattern_comment, content, re.DOTALL)
            for match in matches:
                for group in match:
                    if group:
                        hex_values.add(group)
                        
    except Exception as e:
        print(f"Error reading {filepath}: {e}")
    
    return hex_values

def scan_codebase_for_hex() -> Dict[str, Set[str]]:
    """Scan both Bellscoin and Dogecoin codebases for ALL hex values"""
    all_hex_values = {}
    
    # File extensions to scan
    extensions = ['.cpp', '.h', '.c', '.py', '.sh', '.txt', '.md', '.json', '.conf']
    
    # Scan both directories
    scan_dirs = ['bellscoin', 'dogecoin']
    
    for scan_dir in scan_dirs:
        if not os.path.exists(scan_dir):
            print(f"Directory {scan_dir} not found, skipping...")
            continue
            
        print(f"\n🔍 Scanning {scan_dir} directory...")
        dir_hex_values = set()
        
        for root, dirs, files in os.walk(scan_dir):
            # Skip certain directories
            if any(skip in root for skip in ['.git', '__pycache__', 'obj', '.vs']):
                continue
                
            for file in files:
                if any(file.endswith(ext) for ext in extensions):
                    filepath = os.path.join(root, file)
                    file_hex = extract_all_hex_values(filepath)
                    dir_hex_values.update(file_hex)
                    
                    if file_hex:
                        print(f"  Found {len(file_hex)} hex values in {filepath}")
        
        all_hex_values[scan_dir] = dir_hex_values
        print(f"📊 Total hex values in {scan_dir}: {len(dir_hex_values)}")
    
    return all_hex_values

def test_hex_value_variations(hex_value: str, source: str) -> str:
    """Test all variations of a hex value"""
    candidates = []
    
    # Direct hex value
    candidates.append((f"Direct hex from {source}", hex_value))
    
    # Without 0x prefix (if it had one)
    if hex_value.startswith('0x'):
        clean_hex = hex_value[2:]
        candidates.append((f"Without 0x prefix from {source}", clean_hex))
    else:
        # Add 0x prefix and test without it
        candidates.append((f"With 0x prefix from {source}", "0x" + hex_value))
    
    # SHA256 of the hex value
    hash_result = sha256_hash(hex_value)
    candidates.append((f"SHA256({hex_value}) from {source}", hash_result))
    
    # With Nintondo prefix
    nintondo_combo = sha256_hash("Nintondo" + hex_value)
    candidates.append((f"SHA256('Nintondo' + {hex_value}) from {source}", nintondo_combo))
    
    # With Nintondo suffix  
    nintondo_suffix = sha256_hash(hex_value + "Nintondo")
    candidates.append((f"SHA256({hex_value} + 'Nintondo') from {source}", nintondo_suffix))
    
    # Test each candidate
    for name, candidate in candidates:
        if test_private_key(candidate):
            return candidate
    
    return None

def main():
    """Main execution function"""
    print("🏴‍☠️ COMPREHENSIVE HEX TREASURE HUNT")
    print("=" * 60)
    print("Searching EVERY hex value in Bellscoin and Dogecoin codebases...")
    print(f"Target: {TARGET_PUBKEY[:32]}...")
    
    # Scan both codebases
    all_hex_values = scan_codebase_for_hex()
    
    total_hex_count = sum(len(hex_set) for hex_set in all_hex_values.values())
    print(f"\n📊 TOTAL HEX VALUES FOUND: {total_hex_count}")
    
    # Test all hex values
    tested_count = 0
    
    for source, hex_values in all_hex_values.items():
        print(f"\n🧪 Testing {len(hex_values)} hex values from {source}...")
        
        for i, hex_value in enumerate(sorted(hex_values), 1):
            tested_count += 1
            
            # Only show progress for longer hex values (potential keys)
            if len(hex_value) >= 16:
                print(f"[{tested_count:4d}] Testing {source}: {hex_value[:32]}{'...' if len(hex_value) > 32 else ''}")
            
            result = test_hex_value_variations(hex_value, source)
            if result:
                print(f"\n🎯 TREASURE FOUND!")
                print(f"🔑 Private Key: {result}")
                print(f"📍 Source: {source}")
                print(f"💎 Original hex: {hex_value}")
                return result
    
    print(f"\n💔 No treasure found after testing {tested_count} hex values")
    print("🔍 All discovered hex values have been tested with multiple transformations...")

if __name__ == "__main__":
    main()
