#!/usr/bin/env python3
"""
SPECIFIC HEX VALUES TREASURE HUNT
=================================

This script tests specific hex values we found in the codebase analysis.
Each value is tested with and without 0x prefix, plus various transformations.

Target Genesis Public Key: 040184710fa689ad5023690c80f3a49c8f13f8d45b8c857fbcbc8bc4a8e4d3eb4b10f4d4604fa08dce601aaf0f470216fe1b51850b4acf21b179c45070ac7b03a9
"""

import hashlib
from ecdsa import SigningKey, SECP256k1

# Target genesis public key
TARGET_PUBKEY = "040184710fa689ad5023690c80f3a49c8f13f8d45b8c857fbcbc8bc4a8e4d3eb4b10f4d4604fa08dce601aaf0f470216fe1b51850b4acf21b179c45070ac7b03a9"

def sha256_hash(text: str) -> str:
    """Generate SHA256 hash of text"""
    return hashlib.sha256(text.encode()).hexdigest()

def test_private_key(private_key_hex: str) -> bool:
    """Test if a hex string generates the target public key"""
    try:
        # Remove 0x prefix if present
        if private_key_hex.startswith('0x'):
            private_key_hex = private_key_hex[2:]
        
        # Ensure it's 64 characters (32 bytes)
        if len(private_key_hex) != 64:
            return False
            
        # Convert to integer
        private_key_int = int(private_key_hex, 16)
        
        # Check if it's in valid range for secp256k1
        if private_key_int <= 0 or private_key_int >= 0xFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFEBAAEDCE6AF48A03BBFD25E8CD0364141:
            return False
        
        # Generate public key
        sk = SigningKey.from_secret_exponent(private_key_int, curve=SECP256k1)
        vk = sk.get_verifying_key()
        
        # Get uncompressed public key (65 bytes: 04 + 32 + 32)
        public_key_bytes = b'\x04' + vk.to_string()
        public_key_hex = public_key_bytes.hex()
        
        return public_key_hex.lower() == TARGET_PUBKEY.lower()
        
    except Exception as e:
        return False

def test_specific_hex_values():
    """Test specific hex values found in the codebase"""
    
    # All the specific hex values we found in the analysis
    specific_hex_values = [
        # From Bellscoin
        "9b7bce58999062b63bfb18586813c42491fa32f4591d8d3043cb4fa9e551541b",  # hashGenesisBlock
        "6f80efd038566e1e3eab3e1d38131604d06481e77f2462235c6a9a94b1f8abf9",  # hashMerkleRoot
        "5b2a3f53f605d62c53e62932dac6925e3d74afa5a4b459745c36d42d0ed26a69",  # Expected merkle root
        "e5be24df57c43a82d15c2f06bda961296948f8f8eb48501bed1efb929afe0698",  # Checkpoint hash (our main lead!)
        "040184710fa689ad5023690c80f3a49c8f13f8d45b8c857fbcbc8bc4a8e4d3eb4b10f4d4604fa08dce601aaf0f470216fe1b51850b4acf21b179c45070ac7b03a9",  # Target pubkey
        "caeb449903dc4f0e0ee2",  # PoW value
        
        # From Dogecoin  
        "841a2965955dd288cfa707a755d05a54e45f8bd476835ec9af4402a2b59a2967",  # Checkpoint p1500
        "bd9d26924f05f6daa7f0155f32828ec89e8e29cee9e7121b026a7a3552ac6131",  # Checkpoint p120000
        
        # From test files
        "04678afdb0fe5548271967f1a67130b7105cd6a828e03909a67962e0ea1f61deb649f6bc3f4cef38c4f35504e51ec112de5c384df7ba0b8d578a4c702b6bf11d5f",
        
        # Numeric constants that could be hex
        "486604799",  # Script constant
        "1383509530",  # Bellscoin timestamp
        "1386325540",  # Dogecoin timestamp  
        "44481",      # Bellscoin nNonce
        "99943",      # Dogecoin nNonce
        "11288888",   # From comments
        "1369199888", # From comments
        "1e0ffff0",   # nBits
        
        # Shorter hex values that might be extended
        "1e0ffff0",
        "caeb449903dc4f0e0ee2",
        "4e696e746f6e646f",  # "Nintondo" in hex
    ]
    
    print("🏴‍☠️ SPECIFIC HEX VALUES TREASURE HUNT")
    print("=" * 60)
    print(f"Testing {len(specific_hex_values)} specific hex values from codebase analysis...")
    print(f"Target: {TARGET_PUBKEY[:32]}...")
    
    for i, hex_value in enumerate(specific_hex_values, 1):
        print(f"\n[{i:2d}] Testing: {hex_value}")
        
        # Test variations of each hex value
        candidates = []
        
        # 1. Direct hex value (if 64 chars)
        if len(hex_value) == 64:
            candidates.append(("Direct hex", hex_value))
        
        # 2. Without 0x prefix
        if hex_value.startswith('0x'):
            clean_hex = hex_value[2:]
            if len(clean_hex) == 64:
                candidates.append(("Without 0x", clean_hex))
        
        # 3. SHA256 hash of the hex value
        hash_result = sha256_hash(hex_value)
        candidates.append(("SHA256 hash", hash_result))
        
        # 4. SHA256 with Nintondo prefix
        nintondo_prefix = sha256_hash("Nintondo" + hex_value)
        candidates.append(("SHA256('Nintondo' + hex)", nintondo_prefix))
        
        # 5. SHA256 with Nintondo suffix
        nintondo_suffix = sha256_hash(hex_value + "Nintondo")
        candidates.append(("SHA256(hex + 'Nintondo')", nintondo_suffix))
        
        # 6. If it's a short hex, try padding with zeros
        if len(hex_value) < 64:
            padded_left = hex_value.zfill(64)
            padded_right = hex_value.ljust(64, '0')
            candidates.append(("Left-padded with zeros", padded_left))
            candidates.append(("Right-padded with zeros", padded_right))
        
        # 7. Double SHA256 (Bitcoin style)
        double_hash = sha256_hash(sha256_hash(hex_value))
        candidates.append(("Double SHA256", double_hash))
        
        # Test each candidate
        for name, candidate in candidates:
            print(f"     {name}: {candidate[:32]}{'...' if len(candidate) > 32 else ''}")
            if test_private_key(candidate):
                print(f"\n🎯 TREASURE FOUND!")
                print(f"🔑 Private Key: {candidate}")
                print(f"💎 Original hex: {hex_value}")
                print(f"🔄 Transformation: {name}")
                return candidate
        
        print(f"     ❌ No match for any variation")
    
    print(f"\n💔 No treasure found in specific hex values")
    return None

def test_combinations():
    """Test combinations of known values"""
    print(f"\n🔗 TESTING COMBINATIONS")
    print("=" * 40)
    
    # Key components
    nintondo = "Nintondo"
    checkpoint = "e5be24df57c43a82d15c2f06bda961296948f8f8eb48501bed1efb929afe0698"
    timestamp_bell = "1383509530"
    timestamp_doge = "1386325540"
    nonce_bell = "44481"
    nonce_doge = "99943"
    
    combinations = [
        # Checkpoint + timestamps
        (f"checkpoint + bell_timestamp", checkpoint + timestamp_bell),
        (f"checkpoint + doge_timestamp", checkpoint + timestamp_doge),
        (f"bell_timestamp + checkpoint", timestamp_bell + checkpoint),
        (f"doge_timestamp + checkpoint", timestamp_doge + checkpoint),
        
        # With nonces
        (f"checkpoint + bell_nonce", checkpoint + nonce_bell),
        (f"checkpoint + doge_nonce", checkpoint + nonce_doge),
        
        # Nintondo combinations
        (f"Nintondo + checkpoint + bell_timestamp", nintondo + checkpoint + timestamp_bell),
        (f"Nintondo + checkpoint + doge_timestamp", nintondo + checkpoint + timestamp_doge),
    ]
    
    for name, combo in combinations:
        print(f"Testing: {name}")
        hash_result = sha256_hash(combo)
        print(f"  SHA256: {hash_result}")
        if test_private_key(hash_result):
            print(f"🎯 TREASURE FOUND! {name}")
            print(f"🔑 Private Key: {hash_result}")
            return hash_result
        print(f"  ❌ No match")
    
    return None

def main():
    """Main execution function"""
    # Test specific hex values
    result = test_specific_hex_values()
    if result:
        return result
    
    # Test combinations
    result = test_combinations()
    if result:
        return result
    
    print(f"\n💔 No treasure found in specific analysis")
    print("🔍 Consider running the comprehensive hex hunt...")

if __name__ == "__main__":
    main()
