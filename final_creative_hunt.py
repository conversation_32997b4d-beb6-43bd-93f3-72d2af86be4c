#!/usr/bin/env python3
"""
FINAL CREATIVE TREASURE HUNT
============================

Last attempt with the most creative approaches:
1. Reverse engineering the public key itself
2. Testing mathematical sequences and patterns
3. Testing cryptographic primitives and constants
4. Testing blockchain-specific patterns
5. Testing steganographic approaches

Target Genesis Public Key: 040184710fa689ad5023690c80f3a49c8f13f8d45b8c857fbcbc8bc4a8e4d3eb4b10f4d4604fa08dce601aaf0f470216fe1b51850b4acf21b179c45070ac7b03a9
"""

import hashlib
import math
from ecdsa import SigningKey, SECP256k1

# Target genesis public key
TARGET_PUBKEY = "040184710fa689ad5023690c80f3a49c8f13f8d45b8c857fbcbc8bc4a8e4d3eb4b10f4d4604fa08dce601aaf0f470216fe1b51850b4acf21b179c45070ac7b03a9"

def sha256_hash(text: str) -> str:
    """Generate SHA256 hash of text"""
    return hashlib.sha256(text.encode()).hexdigest()

def test_private_key(private_key_hex: str) -> bool:
    """Test if a hex string generates the target public key"""
    try:
        if private_key_hex.startswith('0x'):
            private_key_hex = private_key_hex[2:]
        
        if len(private_key_hex) != 64:
            return False
            
        private_key_int = int(private_key_hex, 16)
        
        if private_key_int <= 0 or private_key_int >= 0xFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFEBAAEDCE6AF48A03BBFD25E8CD0364141:
            return False
        
        sk = SigningKey.from_secret_exponent(private_key_int, curve=SECP256k1)
        vk = sk.get_verifying_key()
        
        public_key_bytes = b'\x04' + vk.to_string()
        public_key_hex = public_key_bytes.hex()
        
        return public_key_hex.lower() == TARGET_PUBKEY.lower()
        
    except Exception as e:
        return False

def test_pubkey_patterns():
    """Analyze patterns in the public key itself"""
    print("🔍 ANALYZING PUBLIC KEY PATTERNS")
    print("=" * 50)
    
    pubkey = TARGET_PUBKEY.lower()
    
    # Extract parts of the public key
    x_coord = pubkey[2:66]  # X coordinate (32 bytes)
    y_coord = pubkey[66:]   # Y coordinate (32 bytes)
    
    print(f"X coordinate: {x_coord}")
    print(f"Y coordinate: {y_coord}")
    
    # Test various transformations of the coordinates
    candidates = [
        ("X coordinate", x_coord),
        ("Y coordinate", y_coord),
        ("X + Y", x_coord + y_coord),
        ("Y + X", y_coord + x_coord),
        ("SHA256(X)", sha256_hash(x_coord)),
        ("SHA256(Y)", sha256_hash(y_coord)),
        ("SHA256(X+Y)", sha256_hash(x_coord + y_coord)),
        ("X XOR Y", hex(int(x_coord, 16) ^ int(y_coord, 16))[2:].zfill(64)),
        ("X - Y", hex((int(x_coord, 16) - int(y_coord, 16)) & ((1 << 256) - 1))[2:].zfill(64)),
        ("Y - X", hex((int(y_coord, 16) - int(x_coord, 16)) & ((1 << 256) - 1))[2:].zfill(64)),
    ]
    
    for name, candidate in candidates:
        print(f"Testing {name}: {candidate[:32]}...")
        if test_private_key(candidate):
            print(f"🎯 TREASURE FOUND! {name}: {candidate}")
            return candidate
    
    return None

def test_mathematical_sequences():
    """Test mathematical sequences and constants"""
    print("\n🧮 TESTING MATHEMATICAL SEQUENCES")
    print("=" * 50)
    
    # Famous mathematical constants
    constants = [
        ("Pi digits", "31415926535897932384626433832795028841971693993751"),
        ("E digits", "27182818284590452353602874713526624977572470936999"),
        ("Golden ratio", "16180339887498948482045868343656381177203091798057"),
        ("Sqrt(2)", "14142135623730950488016887242096980785696718753769"),
        ("Sqrt(3)", "17320508075688772935274463415058723669428052538103"),
    ]
    
    for name, const_str in constants:
        # Take first 64 characters
        const_64 = const_str[:64].ljust(64, '0')
        print(f"Testing {name}: {const_64[:32]}...")
        if test_private_key(const_64):
            print(f"🎯 TREASURE FOUND! {name}: {const_64}")
            return const_64
        
        # Hash the constant
        hash_result = sha256_hash(const_str)
        if test_private_key(hash_result):
            print(f"🎯 TREASURE FOUND! SHA256({name}): {hash_result}")
            return hash_result
    
    return None

def test_crypto_constants():
    """Test cryptographic constants"""
    print("\n🔐 TESTING CRYPTOGRAPHIC CONSTANTS")
    print("=" * 50)
    
    # secp256k1 curve parameters
    p = 0xFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFEFFFFFC2F
    n = 0xFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFEBAAEDCE6AF48A03BBFD25E8CD0364141
    g_x = 0x79BE667EF9DCBBAC55A06295CE870B07029BFCDB2DCE28D959F2815B16F81798
    g_y = 0x483ADA7726A3C4655DA4FBFC0E1108A8FD17B448A68554199C47D08FFB10D4B8
    
    crypto_constants = [
        ("secp256k1_p", hex(p)[2:]),
        ("secp256k1_n", hex(n)[2:]),
        ("secp256k1_gx", hex(g_x)[2:].zfill(64)),
        ("secp256k1_gy", hex(g_y)[2:].zfill(64)),
        ("p_minus_n", hex(p - n)[2:].zfill(64)),
        ("n_minus_1", hex(n - 1)[2:].zfill(64)),
        ("gx_plus_gy", hex((g_x + g_y) & ((1 << 256) - 1))[2:].zfill(64)),
        ("gx_xor_gy", hex(g_x ^ g_y)[2:].zfill(64)),
    ]
    
    for name, const in crypto_constants:
        print(f"Testing {name}: {const[:32]}...")
        if test_private_key(const):
            print(f"🎯 TREASURE FOUND! {name}: {const}")
            return const
        
        # Hash the constant
        hash_result = sha256_hash(const)
        if test_private_key(hash_result):
            print(f"🎯 TREASURE FOUND! SHA256({name}): {hash_result}")
            return hash_result
    
    return None

def test_blockchain_patterns():
    """Test blockchain-specific patterns"""
    print("\n⛓️ TESTING BLOCKCHAIN PATTERNS")
    print("=" * 50)
    
    # Bitcoin genesis block info
    bitcoin_genesis = "000000000019d6689c085ae165831e934ff763ae46a2a6c172b3f1b60a8ce26f"
    bitcoin_merkle = "4a5e1e4baab89f3a32518a88c31bc87f618f76673e2cc77ab2127b7afdeda33b"
    
    # Test blockchain patterns
    patterns = [
        ("Bitcoin genesis", bitcoin_genesis),
        ("Bitcoin merkle", bitcoin_merkle),
        ("Genesis + Merkle", bitcoin_genesis + bitcoin_merkle),
        ("Merkle + Genesis", bitcoin_merkle + bitcoin_genesis),
        ("Genesis XOR Merkle", hex(int(bitcoin_genesis, 16) ^ int(bitcoin_merkle, 16))[2:].zfill(64)),
    ]
    
    for name, pattern in patterns:
        print(f"Testing {name}: {pattern[:32]}...")
        if len(pattern) == 64 and test_private_key(pattern):
            print(f"🎯 TREASURE FOUND! {name}: {pattern}")
            return pattern
        
        # Hash the pattern
        hash_result = sha256_hash(pattern)
        if test_private_key(hash_result):
            print(f"🎯 TREASURE FOUND! SHA256({name}): {hash_result}")
            return hash_result
    
    return None

def test_steganographic_approaches():
    """Test steganographic hiding methods"""
    print("\n🕵️ TESTING STEGANOGRAPHIC APPROACHES")
    print("=" * 50)
    
    # Look for patterns in the treasure hunt image description
    image_text = "What's buried deep in Bellscoin's 2013 codebase? A long-forgotten key. Whispers say it unlocked Dogecoin's launchpad – and it still works. Crack it first. Retire by Friday."
    
    # Extract various patterns from the text
    words = image_text.lower().replace("'", "").replace(".", "").replace("–", "").replace(",", "").split()
    
    # Test word combinations
    important_words = ["buried", "deep", "bellscoin", "2013", "codebase", "forgotten", "key", "whispers", "unlocked", "dogecoin", "launchpad", "works", "crack", "first", "retire", "friday"]
    
    for word in important_words:
        print(f"Testing word: {word}")
        
        # Hash the word
        hash_result = sha256_hash(word)
        if test_private_key(hash_result):
            print(f"🎯 TREASURE FOUND! Word '{word}': {hash_result}")
            return hash_result
        
        # With Nintondo
        nintondo_combo = sha256_hash("Nintondo" + word)
        if test_private_key(nintondo_combo):
            print(f"🎯 TREASURE FOUND! Nintondo+'{word}': {nintondo_combo}")
            return nintondo_combo
    
    # Test the full text
    hash_result = sha256_hash(image_text)
    if test_private_key(hash_result):
        print(f"🎯 TREASURE FOUND! Full text: {hash_result}")
        return hash_result
    
    return None

def test_simple_patterns():
    """Test very simple patterns that might have been overlooked"""
    print("\n🎯 TESTING SIMPLE PATTERNS")
    print("=" * 50)
    
    simple_patterns = [
        ("All 1s", "1" * 64),
        ("All As", "a" * 64),
        ("All Fs", "f" * 64),
        ("Alternating 01", ("01" * 32)),
        ("Alternating 10", ("10" * 32)),
        ("Alternating af", ("af" * 32)),
        ("Alternating fa", ("fa" * 32)),
        ("Incrementing", "".join(f"{i:02x}" for i in range(32))),
        ("Decrementing", "".join(f"{31-i:02x}" for i in range(32))),
        ("Nintondo repeated", ("4e696e746f6e646f" * 8)),  # "Nintondo" in hex repeated
    ]
    
    for name, pattern in simple_patterns:
        print(f"Testing {name}: {pattern[:32]}...")
        if test_private_key(pattern):
            print(f"🎯 TREASURE FOUND! {name}: {pattern}")
            return pattern
    
    return None

def main():
    """Main execution function"""
    print("🏴‍☠️ FINAL CREATIVE TREASURE HUNT")
    print("=" * 60)
    print("Last attempt with the most creative approaches...")
    print(f"Target: {TARGET_PUBKEY[:32]}...")
    
    # Test all creative approaches
    approaches = [
        test_pubkey_patterns,
        test_mathematical_sequences,
        test_crypto_constants,
        test_blockchain_patterns,
        test_steganographic_approaches,
        test_simple_patterns,
    ]
    
    for approach_func in approaches:
        result = approach_func()
        if result:
            print(f"\n🏆 TREASURE DISCOVERED: {result}")
            return result
    
    print(f"\n💔 No treasure found in final creative analysis")
    print("🤔 The private key might be:")
    print("   1. Generated using a method we haven't considered")
    print("   2. Hidden in a way that requires deeper reverse engineering")
    print("   3. Not actually derivable from the codebase")
    print("   4. Requires additional context or information")

if __name__ == "__main__":
    main()
