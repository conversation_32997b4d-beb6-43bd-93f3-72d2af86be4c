#!/usr/bin/env python3
"""
GENERATE ALL ADDRESSES FROM TARGET PUBLIC KEY
=============================================

Generate Bitcoin, Litecoin, Bellscoin, and Dogecoin addresses from our target public key.

Target Genesis Public Key: 040184710fa689ad5023690c80f3a49c8f13f8d45b8c857fbcbc8bc4a8e4d3eb4b10f4d4604fa08dce601aaf0f470216fe1b51850b4acf21b179c45070ac7b03a9
"""

import hashlib
import base58

# Target genesis public key
TARGET_PUBKEY = "040184710fa689ad5023690c80f3a49c8f13f8d45b8c857fbcbc8bc4a8e4d3eb4b10f4d4604fa08dce601aaf0f470216fe1b51850b4acf21b179c45070ac7b03a9"

def sha256_hash(data):
    """SHA256 hash"""
    return hashlib.sha256(data).digest()

def ripemd160_hash(data):
    """RIPEMD160 hash"""
    return hashlib.new('ripemd160', data).digest()

def hash160(data):
    """Bitcoin-style hash160 (SHA256 then RIPEMD160)"""
    return ripemd160_hash(sha256_hash(data))

def pubkey_to_address_detailed(pubkey_hex, version_byte, crypto_name):
    """Convert public key to address with detailed steps"""
    print(f"\n🔍 GENERATING {crypto_name.upper()} ADDRESS")
    print(f"Version byte: 0x{version_byte:02x} ({version_byte})")
    print("-" * 50)
    
    # Step 1: Convert hex to bytes
    pubkey_bytes = bytes.fromhex(pubkey_hex)
    print(f"1. Public key bytes ({len(pubkey_bytes)} bytes):")
    print(f"   {pubkey_bytes.hex()[:64]}...")
    
    # Step 2: SHA256
    sha256_result = sha256_hash(pubkey_bytes)
    print(f"2. SHA256 of public key:")
    print(f"   {sha256_result.hex()}")
    
    # Step 3: RIPEMD160
    ripemd160_result = ripemd160_hash(sha256_result)
    print(f"3. RIPEMD160 of SHA256 (Hash160):")
    print(f"   {ripemd160_result.hex()}")
    
    # Step 4: Add version byte
    versioned_hash = bytes([version_byte]) + ripemd160_result
    print(f"4. Add version byte:")
    print(f"   {versioned_hash.hex()}")
    
    # Step 5: Double SHA256 for checksum
    checksum_full = sha256_hash(sha256_hash(versioned_hash))
    checksum = checksum_full[:4]
    print(f"5. Double SHA256 for checksum:")
    print(f"   Full: {checksum_full.hex()}")
    print(f"   First 4 bytes: {checksum.hex()}")
    
    # Step 6: Combine versioned hash + checksum
    full_address_bytes = versioned_hash + checksum
    print(f"6. Combine versioned hash + checksum:")
    print(f"   {full_address_bytes.hex()}")
    
    # Step 7: Base58 encode
    address = base58.b58encode(full_address_bytes).decode()
    print(f"7. Base58 encode:")
    print(f"   {address}")
    
    return address

def generate_all_addresses():
    """Generate addresses for all major cryptocurrencies"""
    print("🏴‍☠️ GENERATING ALL ADDRESSES FROM TARGET PUBLIC KEY")
    print("=" * 70)
    print(f"Target public key: {TARGET_PUBKEY}")
    print(f"Key length: {len(TARGET_PUBKEY)} characters")
    
    # Cryptocurrency version bytes
    cryptocurrencies = [
        ("Bitcoin", 0x00),
        ("Bitcoin Script", 0x05),
        ("Litecoin", 0x30),
        ("Litecoin Script", 0x32),
        ("Dogecoin", 0x1e),
        ("Dogecoin Script", 0x16),
        ("Bellscoin", 0x19),
        ("Bellscoin Script", 0x1a),
        ("Bitcoin Testnet", 0x6f),
        ("Dogecoin Testnet", 0xc4),
    ]
    
    addresses = {}
    
    for crypto_name, version_byte in cryptocurrencies:
        address = pubkey_to_address_detailed(TARGET_PUBKEY, version_byte, crypto_name)
        addresses[crypto_name] = address
    
    return addresses

def display_summary(addresses):
    """Display a summary of all generated addresses"""
    print(f"\n📋 SUMMARY OF ALL ADDRESSES")
    print("=" * 70)
    
    # Group by main cryptocurrencies
    main_cryptos = [
        ("Bitcoin", "Bitcoin"),
        ("Litecoin", "Litecoin"), 
        ("Dogecoin", "Dogecoin"),
        ("Bellscoin", "Bellscoin"),
    ]
    
    for crypto_name, key in main_cryptos:
        if key in addresses:
            address = addresses[key]
            print(f"{crypto_name:12}: {address}")
            
            # Show script address if available
            script_key = f"{key} Script"
            if script_key in addresses:
                script_address = addresses[script_key]
                print(f"{crypto_name:12} (Script): {script_address}")
    
    print(f"\nTestnet addresses:")
    testnet_cryptos = [
        ("Bitcoin Testnet", "Bitcoin Testnet"),
        ("Dogecoin Testnet", "Dogecoin Testnet"),
    ]
    
    for crypto_name, key in testnet_cryptos:
        if key in addresses:
            address = addresses[key]
            print(f"{crypto_name:15}: {address}")

def verify_known_addresses(addresses):
    """Verify against known addresses from our investigation"""
    print(f"\n✅ VERIFICATION AGAINST KNOWN ADDRESSES")
    print("=" * 70)
    
    # Known addresses from our investigation
    known_addresses = {
        "Dogecoin": "DQmCZQo3thCvTxkyAhPHfY7DVLqFtJ2ji6",
        "Bellscoin": "BQ5BdsJcLntYNo4Y3bihEukHLpYYNRtY3r",
    }
    
    for crypto_name, expected_address in known_addresses.items():
        if crypto_name in addresses:
            generated_address = addresses[crypto_name]
            if generated_address == expected_address:
                print(f"✅ {crypto_name:12}: MATCH! {generated_address}")
            else:
                print(f"❌ {crypto_name:12}: MISMATCH!")
                print(f"   Expected:  {expected_address}")
                print(f"   Generated: {generated_address}")
        else:
            print(f"⚠️  {crypto_name:12}: Not generated")
    
    # Check if any generated address matches known genesis addresses
    print(f"\n🔍 CHECKING FOR GENESIS BLOCK MATCHES")
    print("-" * 50)
    
    # These are the addresses that receive coins in genesis blocks
    genesis_addresses = [
        "DQmCZQo3thCvTxkyAhPHfY7DVLqFtJ2ji6",  # Dogecoin genesis
        "BQ5BdsJcLntYNo4Y3bihEukHLpYYNRtY3r",  # Bellscoin genesis
    ]
    
    for address in genesis_addresses:
        found = False
        for crypto_name, generated_address in addresses.items():
            if generated_address == address:
                print(f"🎯 GENESIS MATCH: {address} ({crypto_name})")
                found = True
                break
        if not found:
            print(f"❓ Genesis address not found: {address}")

def analyze_address_patterns(addresses):
    """Analyze patterns in the generated addresses"""
    print(f"\n🔬 ADDRESS PATTERN ANALYSIS")
    print("=" * 70)
    
    # Group addresses by starting character
    by_prefix = {}
    for crypto_name, address in addresses.items():
        prefix = address[0]
        if prefix not in by_prefix:
            by_prefix[prefix] = []
        by_prefix[prefix].append((crypto_name, address))
    
    print("Addresses grouped by starting character:")
    for prefix in sorted(by_prefix.keys()):
        print(f"\nStarting with '{prefix}':")
        for crypto_name, address in by_prefix[prefix]:
            print(f"  {crypto_name:20}: {address}")
    
    # Analyze address lengths
    print(f"\nAddress lengths:")
    length_groups = {}
    for crypto_name, address in addresses.items():
        length = len(address)
        if length not in length_groups:
            length_groups[length] = []
        length_groups[length].append((crypto_name, address))
    
    for length in sorted(length_groups.keys()):
        print(f"\nLength {length}:")
        for crypto_name, address in length_groups[length]:
            print(f"  {crypto_name:20}: {address}")

def main():
    """Main execution function"""
    print("🏴‍☠️ COMPREHENSIVE ADDRESS GENERATION")
    print("=" * 70)
    print("Generating addresses for all major cryptocurrencies...")
    
    # Generate all addresses
    addresses = generate_all_addresses()
    
    # Display summary
    display_summary(addresses)
    
    # Verify against known addresses
    verify_known_addresses(addresses)
    
    # Analyze patterns
    analyze_address_patterns(addresses)
    
    print(f"\n🏆 GENERATION COMPLETE!")
    print(f"Generated {len(addresses)} addresses from the target public key.")

if __name__ == "__main__":
    main()
