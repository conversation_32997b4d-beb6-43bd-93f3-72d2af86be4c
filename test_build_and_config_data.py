#!/usr/bin/env python3
"""
TEST BUILD AND CONFIG DATA
==========================

Test build dates, configuration data, and other metadata as potential private keys.

Target Genesis Public Key: 040184710fa689ad5023690c80f3a49c8f13f8d45b8c857fbcbc8bc4a8e4d3eb4b10f4d4604fa08dce601aaf0f470216fe1b51850b4acf21b179c45070ac7b03a9
"""

import hashlib
import datetime
from ecdsa import SigningKey, SECP256k1

# Target genesis public key
TARGET_PUBKEY = "040184710fa689ad5023690c80f3a49c8f13f8d45b8c857fbcbc8bc4a8e4d3eb4b10f4d4604fa08dce601aaf0f470216fe1b51850b4acf21b179c45070ac7b03a9"

def sha256_hash(text: str) -> str:
    """Generate SHA256 hash of text"""
    return hashlib.sha256(text.encode()).hexdigest()

def test_private_key(private_key_hex: str) -> bool:
    """Test if a hex string generates the target public key"""
    try:
        if private_key_hex.startswith('0x'):
            private_key_hex = private_key_hex[2:]
        
        if len(private_key_hex) != 64:
            if len(private_key_hex) < 64:
                private_key_hex = private_key_hex.zfill(64)
            else:
                return False
            
        private_key_int = int(private_key_hex, 16)
        
        if private_key_int <= 0 or private_key_int >= 0xFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFEBAAEDCE6AF48A03BBFD25E8CD0364141:
            return False
        
        sk = SigningKey.from_secret_exponent(private_key_int, curve=SECP256k1)
        vk = sk.get_verifying_key()
        
        public_key_bytes = b'\x04' + vk.to_string()
        public_key_hex = public_key_bytes.hex()
        
        return public_key_hex.lower() == TARGET_PUBKEY.lower()
        
    except Exception as e:
        return False

def test_build_dates():
    """Test build dates and timestamps"""
    print("📅 TESTING BUILD DATES AND TIMESTAMPS")
    print("=" * 60)
    
    # Build date from build.h: "2013-05-26 22:13:27 -0700"
    build_date_candidates = [
        # Full build date string
        ("Full build date", "2013-05-26 22:13:27 -0700"),
        ("Build date no timezone", "2013-05-26 22:13:27"),
        ("Build date compact", "20130526221327"),
        ("Build date YYYYMMDD", "20130526"),
        ("Build date MMDDYYYY", "05262013"),
        ("Build date DDMMYYYY", "26052013"),
        
        # Time components
        ("Build time", "22:13:27"),
        ("Build time compact", "221327"),
        ("Build time no colons", "221327"),
        ("Build hour minute", "2213"),
        ("Build minute second", "1327"),
        
        # May 26, 2013 variations
        ("May 26 2013", "May 26, 2013"),
        ("May262013", "May262013"),
        ("26May2013", "26May2013"),
        ("2013May26", "2013May26"),
        
        # Unix timestamp for May 26, 2013 22:13:27 UTC
        ("Build timestamp UTC", str(int(datetime.datetime(2013, 5, 26, 22, 13, 27).timestamp()))),
        ("Build timestamp PDT", str(int(datetime.datetime(2013, 5, 27, 5, 13, 27).timestamp()))),  # +7 hours for PDT
        
        # Timezone offset
        ("Timezone offset", "-0700"),
        ("Timezone offset no sign", "0700"),
        ("Timezone hours", "7"),
        
        # Day of year (May 26 is day 146 in 2013)
        ("Day of year", "146"),
        ("Day of year 2013", "1462013"),
        ("2013 day 146", "2013146"),
    ]
    
    for name, candidate in build_date_candidates:
        print(f"\n🔍 Testing {name}: '{candidate}'")
        
        # Test direct hash
        hash_result = sha256_hash(candidate)
        if test_private_key(hash_result):
            print(f"🎯 TREASURE FOUND! {name}: {hash_result}")
            return hash_result
        
        # Test with Nintondo
        nintondo_combo = sha256_hash("Nintondo" + candidate)
        if test_private_key(nintondo_combo):
            print(f"🎯 TREASURE FOUND! Nintondo+{name}: {nintondo_combo}")
            return nintondo_combo
        
        # Test candidate + Nintondo
        candidate_nintondo = sha256_hash(candidate + "Nintondo")
        if test_private_key(candidate_nintondo):
            print(f"🎯 TREASURE FOUND! {name}+Nintondo: {candidate_nintondo}")
            return candidate_nintondo
    
    return None

def test_version_and_build_info():
    """Test version numbers and build information"""
    print("\n🔧 TESTING VERSION AND BUILD INFO")
    print("=" * 60)
    
    version_candidates = [
        # Common version patterns for 2013 crypto projects
        ("Version 0.1.0", "0.1.0"),
        ("Version 0.1", "0.1"),
        ("Version 1.0", "1.0"),
        ("Version 1.0.0", "1.0.0"),
        ("Version 0.8", "0.8"),
        ("Version 0.8.0", "0.8.0"),
        ("Version 0.9", "0.9"),
        ("Version 0.9.0", "0.9.0"),
        
        # Build numbers
        ("Build 1", "1"),
        ("Build 100", "100"),
        ("Build 1000", "1000"),
        ("Build 2013", "2013"),
        
        # Git-style versions
        ("Git v0.1.0", "v0.1.0"),
        ("Git commit short", "59887e8"),
        ("Git describe", "v0.6.0-66-g59887e8"),
        ("Git describe dirty", "v0.6.0-66-g59887e8-dirty"),
        
        # Bellscoin specific
        ("Bellscoin", "Bellscoin"),
        ("bellscoin", "bellscoin"),
        ("BELLSCOIN", "BELLSCOIN"),
        ("Bells", "Bells"),
        ("bells", "bells"),
        ("BELLS", "BELLS"),
        
        # Build system info
        ("gcc", "gcc"),
        ("make", "make"),
        ("unix", "unix"),
        ("linux", "linux"),
        ("mingw", "mingw"),
        ("osx", "osx"),
        
        # Copyright year
        ("Copyright 2009", "2009"),
        ("Copyright 2010", "2010"),
        ("Copyright 2013", "2013"),
        ("Satoshi Nakamoto", "Satoshi Nakamoto"),
        ("satoshi", "satoshi"),
    ]
    
    for name, candidate in version_candidates:
        print(f"Testing {name}: '{candidate}'")
        
        # Test direct hash
        hash_result = sha256_hash(candidate)
        if test_private_key(hash_result):
            print(f"🎯 TREASURE FOUND! {name}: {hash_result}")
            return hash_result
        
        # Test with Nintondo
        nintondo_combo = sha256_hash("Nintondo" + candidate)
        if test_private_key(nintondo_combo):
            print(f"🎯 TREASURE FOUND! Nintondo+{name}: {nintondo_combo}")
            return nintondo_combo
    
    return None

def test_makefile_constants():
    """Test constants from makefiles and build system"""
    print("\n🔨 TESTING MAKEFILE CONSTANTS")
    print("=" * 60)
    
    makefile_candidates = [
        # Compiler flags and defines
        ("USE_IPV6", "USE_IPV6"),
        ("BOOST_SPIRIT_THREADSAFE", "BOOST_SPIRIT_THREADSAFE"),
        ("HAVE_BUILD_INFO", "HAVE_BUILD_INFO"),
        ("TEST_DATA_DIR", "TEST_DATA_DIR"),
        ("BOOST_TEST_DYN_LINK", "BOOST_TEST_DYN_LINK"),
        ("_FORTIFY_SOURCE", "_FORTIFY_SOURCE"),
        ("_FORTIFY_SOURCE=2", "_FORTIFY_SOURCE=2"),
        
        # Library names
        ("boost_system", "boost_system"),
        ("boost_filesystem", "boost_filesystem"),
        ("boost_program_options", "boost_program_options"),
        ("boost_thread", "boost_thread"),
        ("db_cxx", "db_cxx"),
        ("ssl", "ssl"),
        ("crypto", "crypto"),
        ("miniupnpc", "miniupnpc"),
        ("pthread", "pthread"),
        
        # Hardening flags
        ("fstack-protector-all", "fstack-protector-all"),
        ("fPIE", "fPIE"),
        ("pie", "pie"),
        ("relro", "relro"),
        ("now", "now"),
        
        # Optimization flags
        ("O2", "O2"),
        ("-O2", "-O2"),
        ("g", "g"),
        ("-g", "-g"),
        
        # Target names
        ("bellsd", "bellsd"),
        ("test_bells", "test_bells"),
        ("FORCE", "FORCE"),
        
        # File extensions
        ("cpp", "cpp"),
        ("o", "o"),
        ("P", "P"),
        ("d", "d"),
        
        # Paths and directories
        ("obj", "obj"),
        ("src", "src"),
        ("test", "test"),
        ("share", "share"),
        ("build", "build"),
    ]
    
    for name, candidate in makefile_candidates:
        print(f"Testing {name}: '{candidate}'")
        
        # Test direct hash
        hash_result = sha256_hash(candidate)
        if test_private_key(hash_result):
            print(f"🎯 TREASURE FOUND! {name}: {hash_result}")
            return hash_result
        
        # Test with Nintondo
        nintondo_combo = sha256_hash("Nintondo" + candidate)
        if test_private_key(nintondo_combo):
            print(f"🎯 TREASURE FOUND! Nintondo+{name}: {nintondo_combo}")
            return nintondo_combo
    
    return None

def test_file_paths_and_names():
    """Test file paths and names as potential keys"""
    print("\n📁 TESTING FILE PATHS AND NAMES")
    print("=" * 60)
    
    file_candidates = [
        # Important file names
        ("main.cpp", "main.cpp"),
        ("checkpoints.cpp", "checkpoints.cpp"),
        ("build.h", "build.h"),
        ("genbuild.sh", "genbuild.sh"),
        ("makefile.unix", "makefile.unix"),
        ("version.cpp", "version.cpp"),
        ("key.cpp", "key.cpp"),
        ("wallet.cpp", "wallet.cpp"),
        
        # Paths
        ("src/main.cpp", "src/main.cpp"),
        ("src/checkpoints.cpp", "src/checkpoints.cpp"),
        ("src/obj/build.h", "src/obj/build.h"),
        ("share/genbuild.sh", "share/genbuild.sh"),
        
        # Project file
        ("bells-qt.pro", "bells-qt.pro"),
        ("bitcoin.qrc", "bitcoin.qrc"),
        
        # License and readme
        ("COPYING", "COPYING"),
        ("INSTALL", "INSTALL"),
        ("README", "README"),
        ("README.md", "README.md"),
        
        # Documentation
        ("Doxyfile", "Doxyfile"),
        ("build-unix.txt", "build-unix.txt"),
        ("release-process.txt", "release-process.txt"),
        
        # Test files
        ("key_tests.cpp", "key_tests.cpp"),
        ("Checkpoints_tests.cpp", "Checkpoints_tests.cpp"),
        ("test_bitcoin.cpp", "test_bitcoin.cpp"),
    ]
    
    for name, candidate in file_candidates:
        print(f"Testing {name}: '{candidate}'")
        
        # Test direct hash
        hash_result = sha256_hash(candidate)
        if test_private_key(hash_result):
            print(f"🎯 TREASURE FOUND! {name}: {hash_result}")
            return hash_result
        
        # Test with Nintondo
        nintondo_combo = sha256_hash("Nintondo" + candidate)
        if test_private_key(nintondo_combo):
            print(f"🎯 TREASURE FOUND! Nintondo+{name}: {nintondo_combo}")
            return nintondo_combo
    
    return None

def main():
    """Main execution function"""
    print("🏴‍☠️ BUILD AND CONFIG DATA TREASURE HUNT")
    print("=" * 60)
    print("Testing build dates, version info, and configuration data...")
    print(f"Target: {TARGET_PUBKEY[:32]}...")
    
    # Test different approaches
    approaches = [
        test_build_dates,
        test_version_and_build_info,
        test_makefile_constants,
        test_file_paths_and_names,
    ]
    
    for approach_func in approaches:
        result = approach_func()
        if result:
            print(f"\n🏆 TREASURE DISCOVERED: {result}")
            return result
    
    print(f"\n💔 No treasure found in build and config data")
    print("🤔 The private key might be hidden in an even more obscure location...")

if __name__ == "__main__":
    main()
