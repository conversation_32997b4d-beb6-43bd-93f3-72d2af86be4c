#!/usr/bin/env python3
"""
CHECK TEST KEY ADDRESSES
========================

Check if the test keys from Bellscoin/Dogecoin generate the address
where the 88 coins are sent in the genesis block.

We know the public key, let's see what address it generates and compare
with the test key addresses.

Target Genesis Public Key: 040184710fa689ad5023690c80f3a49c8f13f8d45b8c857fbcbc8bc4a8e4d3eb4b10f4d4604fa08dce601aaf0f470216fe1b51850b4acf21b179c45070ac7b03a9
"""

import hashlib
import base58
from ecdsa import Signing<PERSON>ey, SECP256k1

# Target genesis public key
TARGET_PUBKEY = "040184710fa689ad5023690c80f3a49c8f13f8d45b8c857fbcbc8bc4a8e4d3eb4b10f4d4604fa08dce601aaf0f470216fe1b51850b4acf21b179c45070ac7b03a9"

def sha256_hash(data):
    """SHA256 hash"""
    return hashlib.sha256(data).digest()

def ripemd160_hash(data):
    """RIPEMD160 hash"""
    return hashlib.new('ripemd160', data).digest()

def hash160(data):
    """Bitcoin-style hash160 (SHA256 then RIPEMD160)"""
    return ripemd160_hash(sha256_hash(data))

def pubkey_to_address(pubkey_hex, version_byte=0x30):
    """Convert public key to address (Bellscoin/Dogecoin style)"""
    try:
        # Convert hex to bytes
        pubkey_bytes = bytes.fromhex(pubkey_hex)
        
        # Hash160
        hash160_result = hash160(pubkey_bytes)
        
        # Add version byte
        versioned_hash = bytes([version_byte]) + hash160_result
        
        # Double SHA256 for checksum
        checksum = sha256_hash(sha256_hash(versioned_hash))[:4]
        
        # Combine and encode
        full_address = versioned_hash + checksum
        address = base58.b58encode(full_address).decode()
        
        return address
    except Exception as e:
        print(f"Error converting pubkey to address: {e}")
        return None

def decode_wif_key(wif_key):
    """Decode a WIF (Wallet Import Format) key to hex"""
    try:
        # Decode base58
        decoded = base58.b58decode(wif_key)
        
        # Remove version byte and checksum
        if len(decoded) == 37:  # Uncompressed
            private_key = decoded[1:33]
        elif len(decoded) == 38:  # Compressed
            private_key = decoded[1:33]
        else:
            return None
            
        return private_key.hex()
        
    except Exception as e:
        print(f"Error decoding WIF key {wif_key}: {e}")
        return None

def private_key_to_public_key(private_key_hex):
    """Convert private key to public key"""
    try:
        private_key_int = int(private_key_hex, 16)
        sk = SigningKey.from_secret_exponent(private_key_int, curve=SECP256k1)
        vk = sk.get_verifying_key()
        
        # Get uncompressed public key (65 bytes: 04 + 32 + 32)
        public_key_bytes = b'\x04' + vk.to_string()
        return public_key_bytes.hex()
        
    except Exception as e:
        print(f"Error converting private key to public key: {e}")
        return None

def main():
    """Main execution function"""
    print("🔍 CHECKING TEST KEY ADDRESSES")
    print("=" * 60)
    
    # First, let's see what address our target public key generates
    target_address_bell = pubkey_to_address(TARGET_PUBKEY, 0x30)  # Bellscoin version byte
    target_address_doge = pubkey_to_address(TARGET_PUBKEY, 0x1e)  # Dogecoin version byte
    
    print(f"Target public key: {TARGET_PUBKEY}")
    print(f"Target address (Bellscoin): {target_address_bell}")
    print(f"Target address (Dogecoin): {target_address_doge}")
    
    # Test keys from both Bellscoin and Dogecoin test files
    test_keys = [
        ("strSecret1", "6uu5bsZLA2Lm6yCxgwxDxHyZmhYeqBMLQT83Fyq738YhYucQPQf"),
        ("strSecret2", "6vZDRwYgTNidWzmKs9x8QzQGeWCqbdUtNRpEKZMaP67ZSn8XMjb"),
        ("strSecret1C", "T6UsJv9hYpvDfM5noKYkB3vfeHxhyegkeWJ4y7qKeQJuyXMK11XX"),
        ("strSecret2C", "T9PBs5kq9QrkBPxeGNWKitMi4XuFVr25jaXTnuopLVZxCUAJbixA"),
    ]
    
    # Test addresses from Bellscoin test file
    test_addresses = [
        ("addr1", "LWaFezDtucfCA4xcVEfs3R3xfgGWjSwcZr"),
        ("addr2", "LXwHM6mRd432EzLJYwuKQMPhTzrgr7ur9K"),
        ("addr1C", "LZWK8h7C166niP6GmpUmiGrvn4oxPqQgFV"),
        ("addr2C", "Lgb6tdqmdW3n5E12johSuEAqRMt4kAr7yu"),
    ]
    
    print(f"\n📋 TEST ADDRESSES FROM BELLSCOIN:")
    for name, addr in test_addresses:
        print(f"  {name}: {addr}")
        if addr == target_address_bell:
            print(f"  🎯 MATCH! {name} matches target Bellscoin address!")
        if addr == target_address_doge:
            print(f"  🎯 MATCH! {name} matches target Dogecoin address!")
    
    print(f"\n🔑 ANALYZING TEST KEYS:")
    for name, wif_key in test_keys:
        print(f"\n{name}: {wif_key}")
        
        # Decode WIF to hex
        hex_key = decode_wif_key(wif_key)
        if hex_key:
            print(f"  Hex: {hex_key}")
            
            # Convert to public key
            pubkey = private_key_to_public_key(hex_key)
            if pubkey:
                print(f"  Public key: {pubkey}")
                
                # Check if this matches our target
                if pubkey.lower() == TARGET_PUBKEY.lower():
                    print(f"  🎯 TREASURE FOUND! {name} generates the target public key!")
                    print(f"  🔑 Private key: {hex_key}")
                    return hex_key
                
                # Generate addresses
                addr_bell = pubkey_to_address(pubkey, 0x30)
                addr_doge = pubkey_to_address(pubkey, 0x1e)
                
                print(f"  Address (Bellscoin): {addr_bell}")
                print(f"  Address (Dogecoin): {addr_doge}")
                
                # Check if addresses match target
                if addr_bell == target_address_bell:
                    print(f"  🎯 MATCH! Bellscoin address matches!")
                if addr_doge == target_address_doge:
                    print(f"  🎯 MATCH! Dogecoin address matches!")
        else:
            print(f"  ❌ Failed to decode WIF key")
    
    # Let's also try different version bytes for address generation
    print(f"\n🔄 TRYING DIFFERENT VERSION BYTES:")
    version_bytes = [
        (0x00, "Bitcoin"),
        (0x05, "Bitcoin Script"),
        (0x30, "Bellscoin"),
        (0x1e, "Dogecoin"),
        (0x32, "Litecoin"),
        (0x16, "Litecoin Script"),
        (0x6f, "Bitcoin Testnet"),
        (0xc4, "Dogecoin Testnet"),
    ]
    
    for version_byte, name in version_bytes:
        addr = pubkey_to_address(TARGET_PUBKEY, version_byte)
        print(f"  {name} (0x{version_byte:02x}): {addr}")
    
    # Let's also check if the target public key appears in any format in the codebase
    print(f"\n🔍 CHECKING FOR PUBLIC KEY PATTERNS:")
    
    # Check if any part of the public key appears as a private key
    pubkey_parts = [
        TARGET_PUBKEY[2:66],   # X coordinate
        TARGET_PUBKEY[66:],    # Y coordinate
        TARGET_PUBKEY[2:34],   # First 16 bytes of X
        TARGET_PUBKEY[34:66],  # Last 16 bytes of X
        TARGET_PUBKEY[66:98],  # First 16 bytes of Y
        TARGET_PUBKEY[98:],    # Last 16 bytes of Y
    ]
    
    for i, part in enumerate(pubkey_parts):
        if len(part) == 64:  # Only test 32-byte parts
            print(f"  Testing pubkey part {i}: {part[:32]}...")
            
            # Try as private key
            try:
                private_key_int = int(part, 16)
                if 0 < private_key_int < 0xFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFEBAAEDCE6AF48A03BBFD25E8CD0364141:
                    sk = SigningKey.from_secret_exponent(private_key_int, curve=SECP256k1)
                    vk = sk.get_verifying_key()
                    
                    public_key_bytes = b'\x04' + vk.to_string()
                    generated_pubkey = public_key_bytes.hex()
                    
                    if generated_pubkey.lower() == TARGET_PUBKEY.lower():
                        print(f"  🎯 TREASURE FOUND! Pubkey part {i} is the private key: {part}")
                        return part
            except:
                pass
    
    print(f"\n💔 No direct matches found in test keys")
    print(f"🤔 The private key might be hidden in a different way...")

if __name__ == "__main__":
    main()
