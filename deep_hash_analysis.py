#!/usr/bin/env python3
"""
Deep Analysis of the Bellscoin Genesis Hash
===========================================

Analyzing the hash: 0xe5be24df57c43a82d15c2f06bda961296948f8f8eb48501bed1efb929afe0698

This hash appears in Bellscoin's checkpoints.cpp as the genesis block hash,
but it might also be a private key or contain encoded information.

Target Genesis Public Key: 040184710fa689ad5023690c80f3a49c8f13f8d45b8c857fbcbc8bc4a8e4d3eb4b10f4d4604fa08dce601aaf0f470216fe1b51850b4acf21b179c45070ac7b03a9
"""

import hashlib
import binascii
import struct
from typing import Optional, List, Tuple
from ecdsa import SigningKey, SECP256k1
from ecdsa.util import string_to_number, number_to_string

# Target genesis public key
TARGET_PUBKEY = "040184710fa689ad5023690c80f3a49c8f13f8d45b8c857fbcbc8bc4a8e4d3eb4b10f4d4604fa08dce601aaf0f470216fe1b51850b4acf21b179c45070ac7b03a9"

# The mysterious hash
BELLSCOIN_HASH = "e5be24df57c43a82d15c2f06bda961296948f8f8eb48501bed1efb929afe0698"

def test_private_key(private_key_hex: str) -> bool:
    """Test if a private key generates the target public key"""
    try:
        if len(private_key_hex) != 64:
            return False
            
        private_key_int = int(private_key_hex, 16)
        if private_key_int <= 0 or private_key_int >= 0xFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFEBAAEDCE6AF48A03BBFD25E8CD0364141:
            return False
            
        signing_key = SigningKey.from_string(binascii.unhexlify(private_key_hex), curve=SECP256k1)
        verifying_key = signing_key.get_verifying_key()
        public_key_bytes = verifying_key.to_string()
        public_key_hex = "04" + public_key_bytes.hex()
        
        return public_key_hex.lower() == TARGET_PUBKEY.lower()
        
    except Exception as e:
        return False

def test_candidate(name: str, private_key_hex: str) -> bool:
    """Test a candidate and print results"""
    print(f"\n🔍 Testing: {name}")
    print(f"   Private Key: {private_key_hex}")
    
    if test_private_key(private_key_hex):
        print(f"   🎯 TREASURE FOUND! {name}")
        return True
    else:
        print(f"   ❌ No match")
        return False

def analyze_hash_structure():
    """Analyze the structure and patterns in the hash"""
    print("🔬 DEEP HASH STRUCTURE ANALYSIS")
    print("=" * 60)
    
    hash_bytes = bytes.fromhex(BELLSCOIN_HASH)
    
    print(f"Original Hash: {BELLSCOIN_HASH}")
    print(f"Length: {len(BELLSCOIN_HASH)} characters ({len(hash_bytes)} bytes)")
    print(f"As Integer: {int(BELLSCOIN_HASH, 16)}")
    print(f"Binary: {bin(int(BELLSCOIN_HASH, 16))}")
    
    # Analyze byte patterns
    print(f"\nByte Analysis:")
    for i in range(0, len(BELLSCOIN_HASH), 2):
        byte_hex = BELLSCOIN_HASH[i:i+2]
        byte_val = int(byte_hex, 16)
        print(f"  Byte {i//2:2d}: 0x{byte_hex} = {byte_val:3d} = '{chr(byte_val) if 32 <= byte_val <= 126 else '.'}'")
    
    # Look for patterns
    print(f"\nPattern Analysis:")
    
    # Check for repeated sequences
    for length in [2, 4, 6, 8]:
        for i in range(len(BELLSCOIN_HASH) - length):
            pattern = BELLSCOIN_HASH[i:i+length]
            count = BELLSCOIN_HASH.count(pattern)
            if count > 1:
                print(f"  Repeated pattern '{pattern}' appears {count} times")
    
    # Check for arithmetic sequences
    bytes_list = [int(BELLSCOIN_HASH[i:i+2], 16) for i in range(0, len(BELLSCOIN_HASH), 2)]
    print(f"  Bytes as sequence: {bytes_list}")
    
    # Check for specific meaningful patterns
    meaningful_patterns = {
        "deadbeef": "Dead Beef",
        "cafebabe": "Cafe Babe", 
        "feedface": "Feed Face",
        "c0ffee": "Coffee",
        "1337": "Leet",
        "beef": "Beef",
        "dead": "Dead",
        "cafe": "Cafe",
        "babe": "Babe",
        "face": "Face",
        "feed": "Feed",
    }
    
    for pattern, name in meaningful_patterns.items():
        if pattern in BELLSCOIN_HASH.lower():
            print(f"  Found meaningful pattern: {pattern} ({name})")

def generate_hash_transformations() -> List[Tuple[str, str]]:
    """Generate various transformations of the hash"""
    candidates = []
    
    # Direct hash as private key
    candidates.append(("Direct Hash", BELLSCOIN_HASH))
    
    # Reverse the hash
    reversed_hash = BELLSCOIN_HASH[::-1]
    candidates.append(("Reversed Hash", reversed_hash))
    
    # Byte-reverse (reverse each pair)
    byte_reversed = ''.join([BELLSCOIN_HASH[i:i+2] for i in range(0, len(BELLSCOIN_HASH), 2)][::-1])
    candidates.append(("Byte-Reversed Hash", byte_reversed))
    
    # XOR with common patterns
    hash_int = int(BELLSCOIN_HASH, 16)
    
    xor_patterns = {
        "All 0xFF": 0xFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF,
        "All 0xAA": 0xAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA,
        "All 0x55": 0x5555555555555555555555555555555555555555555555555555555555555555,
        "Alternating": 0xA5A5A5A5A5A5A5A5A5A5A5A5A5A5A5A5A5A5A5A5A5A5A5A5A5A5A5A5A5A5A5A5,
    }
    
    for name, pattern in xor_patterns.items():
        xor_result = hex(hash_int ^ pattern)[2:].zfill(64)
        candidates.append((f"Hash XOR {name}", xor_result))
    
    # Arithmetic operations
    candidates.extend([
        ("Hash + 1", hex((hash_int + 1) % (2**256))[2:].zfill(64)),
        ("Hash - 1", hex((hash_int - 1) % (2**256))[2:].zfill(64)),
        ("Hash * 2", hex((hash_int * 2) % (2**256))[2:].zfill(64)),
        ("Hash / 2", hex(hash_int // 2)[2:].zfill(64)),
    ])
    
    # Bit operations
    candidates.extend([
        ("Hash << 1", hex((hash_int << 1) % (2**256))[2:].zfill(64)),
        ("Hash >> 1", hex(hash_int >> 1)[2:].zfill(64)),
        ("Hash Complement", hex((~hash_int) % (2**256))[2:].zfill(64)),
    ])
    
    # Hash of hash
    candidates.extend([
        ("SHA256(Hash)", hashlib.sha256(BELLSCOIN_HASH.encode()).hexdigest()),
        ("SHA256(Hash bytes)", hashlib.sha256(bytes.fromhex(BELLSCOIN_HASH)).hexdigest()),
        ("Double SHA256", hashlib.sha256(hashlib.sha256(bytes.fromhex(BELLSCOIN_HASH)).digest()).hexdigest()),
    ])
    
    # Split and recombine
    half1 = BELLSCOIN_HASH[:32]
    half2 = BELLSCOIN_HASH[32:]
    candidates.extend([
        ("First Half + First Half", half1 + half1),
        ("Second Half + Second Half", half2 + half2),
        ("Second Half + First Half", half2 + half1),
        ("SHA256(First Half)", hashlib.sha256(half1.encode()).hexdigest()),
        ("SHA256(Second Half)", hashlib.sha256(half2.encode()).hexdigest()),
    ])
    
    # Quarters
    q1, q2, q3, q4 = BELLSCOIN_HASH[:16], BELLSCOIN_HASH[16:32], BELLSCOIN_HASH[32:48], BELLSCOIN_HASH[48:]
    candidates.extend([
        ("Q1+Q2+Q3+Q4", q1 + q2 + q3 + q4),  # Original
        ("Q4+Q3+Q2+Q1", q4 + q3 + q2 + q1),  # Reverse quarters
        ("Q1+Q3+Q2+Q4", q1 + q3 + q2 + q4),  # Swap middle
        ("Q2+Q1+Q4+Q3", q2 + q1 + q4 + q3),  # Swap pairs
    ])
    
    return candidates

def analyze_as_encoded_data():
    """Analyze if the hash contains encoded data"""
    print("\n🔍 ENCODED DATA ANALYSIS")
    print("=" * 40)
    
    hash_bytes = bytes.fromhex(BELLSCOIN_HASH)
    
    # Try to interpret as different data types
    print("Interpreting as different data types:")
    
    # As 8 32-bit integers
    if len(hash_bytes) >= 32:
        ints = struct.unpack('>8I', hash_bytes)  # Big-endian
        print(f"  As 8 uint32 (BE): {ints}")
        
        ints_le = struct.unpack('<8I', hash_bytes)  # Little-endian
        print(f"  As 8 uint32 (LE): {ints_le}")
    
    # As 4 64-bit integers
    if len(hash_bytes) >= 32:
        longs = struct.unpack('>4Q', hash_bytes)
        print(f"  As 4 uint64 (BE): {longs}")
        
        longs_le = struct.unpack('<4Q', hash_bytes)
        print(f"  As 4 uint64 (LE): {longs_le}")
    
    # Check if any of these integers could be timestamps
    print("\nChecking for timestamp patterns:")
    all_ints = list(ints) + list(ints_le) + list(longs) + list(longs_le)
    
    for i, val in enumerate(all_ints):
        # Check if it's a reasonable Unix timestamp (between 2009-2025)
        if 1230768000 <= val <= 1735689600:  # Jan 1 2009 to Jan 1 2025
            import datetime
            dt = datetime.datetime.fromtimestamp(val)
            print(f"  Integer {i}: {val} = {dt}")
    
    # Try base64 decode
    try:
        import base64
        # Pad if necessary
        padded = BELLSCOIN_HASH + '=' * (4 - len(BELLSCOIN_HASH) % 4)
        decoded = base64.b64decode(padded, validate=True)
        print(f"Base64 decode attempt: {decoded.hex()}")
    except:
        print("Not valid base64")

def main():
    """Main execution function"""
    print("🕵️ DEEP ANALYSIS OF BELLSCOIN GENESIS HASH")
    print("=" * 70)
    print(f"Target Public Key: {TARGET_PUBKEY}")
    print(f"Analyzing Hash: {BELLSCOIN_HASH}")
    print("=" * 70)
    
    # Structural analysis
    analyze_hash_structure()
    
    # Encoded data analysis
    analyze_as_encoded_data()
    
    # Test transformations
    print(f"\n🧪 TESTING HASH TRANSFORMATIONS")
    print("=" * 40)
    
    candidates = generate_hash_transformations()
    total_candidates = len(candidates)
    
    print(f"Testing {total_candidates} transformations...")
    
    for i, (name, private_key_hex) in enumerate(candidates, 1):
        print(f"\n[{i}/{total_candidates}] ", end="")
        
        if test_candidate(name, private_key_hex):
            print(f"\n🎉 SUCCESS! The treasure has been found!")
            print(f"🎉 Transformation: {name}")
            print(f"🎉 Private key: {private_key_hex}")
            return private_key_hex
    
    print(f"\n❌ No matches found in {total_candidates} transformations")
    print("💡 The hash might need a more complex transformation or combination")
    return None

if __name__ == "__main__":
    result = main()
    if result:
        print(f"\n🏆 FINAL RESULT: {result}")
