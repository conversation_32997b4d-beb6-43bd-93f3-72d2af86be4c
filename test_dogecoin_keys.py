#!/usr/bin/env python3
"""
TEST DOGECOIN PRIVATE KEYS
=========================

Test the private keys found in <PERSON><PERSON><PERSON><PERSON>'s key_tests.cpp file.
These are WIF format keys that we need to decode and test.

Target Genesis Public Key: 040184710fa689ad5023690c80f3a49c8f13f8d45b8c857fbcbc8bc4a8e4d3eb4b10f4d4604fa08dce601aaf0f470216fe1b51850b4acf21b179c45070ac7b03a9
"""

import hashlib
import base58
from ecdsa import SigningKey, SECP256k1

# Target genesis public key
TARGET_PUBKEY = "040184710fa689ad5023690c80f3a49c8f13f8d45b8c857fbcbc8bc4a8e4d3eb4b10f4d4604fa08dce601aaf0f470216fe1b51850b4acf21b179c45070ac7b03a9"

def sha256_hash(text: str) -> str:
    """Generate SHA256 hash of text"""
    return hashlib.sha256(text.encode()).hexdigest()

def test_private_key(private_key_hex: str) -> bool:
    """Test if a hex string generates the target public key"""
    try:
        # Remove 0x prefix if present
        if private_key_hex.startswith('0x'):
            private_key_hex = private_key_hex[2:]
        
        # Ensure it's 64 characters (32 bytes)
        if len(private_key_hex) != 64:
            return False
            
        # Convert to integer
        private_key_int = int(private_key_hex, 16)
        
        # Check if it's in valid range for secp256k1
        if private_key_int <= 0 or private_key_int >= 0xFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFEBAAEDCE6AF48A03BBFD25E8CD0364141:
            return False
        
        # Generate public key
        sk = SigningKey.from_secret_exponent(private_key_int, curve=SECP256k1)
        vk = sk.get_verifying_key()
        
        # Get uncompressed public key (65 bytes: 04 + 32 + 32)
        public_key_bytes = b'\x04' + vk.to_string()
        public_key_hex = public_key_bytes.hex()
        
        return public_key_hex.lower() == TARGET_PUBKEY.lower()
        
    except Exception as e:
        return False

def decode_wif_key(wif_key: str) -> str:
    """Decode a WIF (Wallet Import Format) key to hex"""
    try:
        # Decode base58
        decoded = base58.b58decode(wif_key)
        
        # Remove version byte and checksum
        if len(decoded) == 37:  # Uncompressed
            private_key = decoded[1:33]
        elif len(decoded) == 38:  # Compressed
            private_key = decoded[1:33]
        else:
            return None
            
        return private_key.hex()
        
    except Exception as e:
        print(f"Error decoding WIF key {wif_key}: {e}")
        return None

def test_dogecoin_test_keys():
    """Test the private keys from Dogecoin's key_tests.cpp"""
    print("🐕 TESTING DOGECOIN TEST KEYS")
    print("=" * 50)
    
    # Keys from dogecoin/src/test/key_tests.cpp
    test_keys = [
        ("strSecret1", "6uu5bsZLA2Lm6yCxgwxDxHyZmhYeqBMLQT83Fyq738YhYucQPQf"),
        ("strSecret2", "6vZDRwYgTNidWzmKs9x8QzQGeWCqbdUtNRpEKZMaP67ZSn8XMjb"),
        ("strSecret1C", "T6UsJv9hYpvDfM5noKYkB3vfeHxhyegkeWJ4y7qKeQJuyXMK11XX"),
        ("strSecret2C", "T9PBs5kq9QrkBPxeGNWKitMi4XuFVr25jaXTnuopLVZxCUAJbixA"),
    ]
    
    for name, wif_key in test_keys:
        print(f"\nTesting {name}: {wif_key}")
        
        # Decode WIF to hex
        hex_key = decode_wif_key(wif_key)
        if hex_key:
            print(f"  Decoded hex: {hex_key}")
            
            # Test direct key
            if test_private_key(hex_key):
                print(f"🎯 TREASURE FOUND! {name}: {hex_key}")
                return hex_key
            
            # Test SHA256 of the hex key
            hash_result = sha256_hash(hex_key)
            print(f"  SHA256: {hash_result}")
            if test_private_key(hash_result):
                print(f"🎯 TREASURE FOUND! SHA256({name}): {hash_result}")
                return hash_result
            
            # Test with Nintondo
            nintondo_combo = sha256_hash("Nintondo" + hex_key)
            print(f"  Nintondo+: {nintondo_combo[:32]}...")
            if test_private_key(nintondo_combo):
                print(f"🎯 TREASURE FOUND! Nintondo+{name}: {nintondo_combo}")
                return nintondo_combo
        else:
            print(f"  ❌ Failed to decode WIF key")
        
        print(f"  ❌ No match")
    
    return None

def test_bellscoin_test_keys():
    """Check if Bellscoin has similar test keys"""
    print("\n🔔 CHECKING BELLSCOIN FOR TEST KEYS")
    print("=" * 50)
    
    try:
        with open('bellscoin/src/test/key_tests.cpp', 'r') as f:
            content = f.read()
            
            # Look for WIF-like strings
            import re
            wif_pattern = r'["\']([5-9A-HJ-NP-U][1-9A-HJ-NP-Za-km-z]{50,})["\']'
            matches = re.findall(wif_pattern, content)
            
            if matches:
                print(f"Found {len(matches)} potential WIF keys in Bellscoin:")
                for i, wif_key in enumerate(matches, 1):
                    print(f"  [{i}] {wif_key}")
                    
                    hex_key = decode_wif_key(wif_key)
                    if hex_key:
                        print(f"      Hex: {hex_key}")
                        if test_private_key(hex_key):
                            print(f"🎯 TREASURE FOUND! Bellscoin test key: {hex_key}")
                            return hex_key
                    print(f"      ❌ No match")
            else:
                print("No WIF keys found in Bellscoin test files")
                
    except FileNotFoundError:
        print("Bellscoin key_tests.cpp not found")
    
    return None

def test_key_combinations():
    """Test combinations of the decoded keys"""
    print("\n🔗 TESTING KEY COMBINATIONS")
    print("=" * 40)
    
    # First decode all keys
    test_keys = [
        "6uu5bsZLA2Lm6yCxgwxDxHyZmhYeqBMLQT83Fyq738YhYucQPQf",
        "6vZDRwYgTNidWzmKs9x8QzQGeWCqbdUtNRpEKZMaP67ZSn8XMjb",
        "T6UsJv9hYpvDfM5noKYkB3vfeHxhyegkeWJ4y7qKeQJuyXMK11XX",
        "T9PBs5kq9QrkBPxeGNWKitMi4XuFVr25jaXTnuopLVZxCUAJbixA",
    ]
    
    decoded_keys = []
    for wif_key in test_keys:
        hex_key = decode_wif_key(wif_key)
        if hex_key:
            decoded_keys.append(hex_key)
    
    print(f"Decoded {len(decoded_keys)} keys successfully")
    
    # Test XOR combinations
    for i, key1 in enumerate(decoded_keys):
        for j, key2 in enumerate(decoded_keys):
            if i != j:
                try:
                    key1_int = int(key1, 16)
                    key2_int = int(key2, 16)
                    xor_result = key1_int ^ key2_int
                    xor_hex = hex(xor_result)[2:].zfill(64)
                    
                    print(f"XOR key{i+1} ^ key{j+1}: {xor_hex[:32]}...")
                    if test_private_key(xor_hex):
                        print(f"🎯 TREASURE FOUND! XOR combination: {xor_hex}")
                        return xor_hex
                except:
                    continue
    
    return None

def main():
    """Main execution function"""
    print("🏴‍☠️ TESTING DOGECOIN TEST KEYS")
    print("=" * 60)
    print("Testing private keys from Dogecoin's test files...")
    print(f"Target: {TARGET_PUBKEY[:32]}...")
    
    # Test different approaches
    tests = [
        test_dogecoin_test_keys,
        test_bellscoin_test_keys,
        test_key_combinations,
    ]
    
    for test_func in tests:
        result = test_func()
        if result:
            print(f"\n🏆 TREASURE DISCOVERED: {result}")
            return result
    
    print(f"\n💔 No treasure found in test keys")

if __name__ == "__main__":
    main()
